# -*- coding: UTF-8 -*-
"""
股票买卖点分析工具 (Trading Signals Analyzer)

0 30 19 * * 1-5

主要功能:
1. 多维度技术指标分析
2. 基本面分析评分
3. 风口面分析评分
4. 资金面分析评分
5. 市场面分析评分
6. 风险面分析评分
7. 宏观面分析评分
8. 行为面分析评分
9. 综合加权评分系统
10. 买卖点信号生成
11. 风险控制建议
12. 历史回测验证
13. 实时监控预警


个股、板块、市场、宏观四个层次的分析

分析维度:
- 技术面: MA、MACD、KDJ、RSI、布林带、ATR、CCI等
- 基本面: 估值、财务、成长性、盈利能力
- 风口面: 行业热度、政策支持、市场关注度
- 资金面: 流动性、资金流向、换手率、北向资金
- 综合评分: 多因子加权评分

- 宏观面: 利率、通胀、政策环境
- 市场面: 市场情绪、板块轮动、市场结构
- 风险面: 波动性、流动性风险、系统性风险
- 行为面: 投资者行为、市场微观结构


关于数据：历史行情只有成交数据，其它基本面数据只能使用实时数据


使用方式:
python tools/stock_trading_signals.py -c 000001 -d 20250710
python tools/stock_trading_signals.py -c 000001 -b 20250601 -e 20250710
python tools/stock_trading_signals.py --scan --date 20250710
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import argparse
import time
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, StockHistory, StockCode, Daliy, StockQuant, QuantList
from sqlalchemy import and_, desc, func
from sqlalchemy.dialects.mysql import insert

# 工具类
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.helper import ago_day_timestr, sz_or_sh, check_bool, isLimit, cal_price_momentum
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler,
    register_cleanup,
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from config.settings import get_settings

# 模拟数据管理
from config.mock_data_config import get_mock_data_manager, MockDataSettings, MockDataMode, configure_mock_data

# 优化的评分算法
from algorithms.optimized_scoring_algorithm import OptimizedScoringAlgorithm, ScoringWeights

# 数据获取
from db.fetch import (
    fetch_all_stock, fetch_quant, fetch_quant_raw, quant_monitor,
    fetch_all_kline, fetch_all_history, fetch_quant_stock, is_trade_date,
    fetch_all_stock_data, fetch_all_stock_source
)

# 技术分析库
import talib
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier

# 统计分析库
try:
    import statsmodels.api as sm
except ImportError:
    logger.warning("statsmodels 未安装，CAPM计算将被跳过")
    sm = None

# 优化的分析器
try:
    from .optimized_stock_analyzer import OptimizedStockAnalyzer
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from optimized_stock_analyzer import OptimizedStockAnalyzer
    except ImportError:
        # 如果都失败了，创建一个占位符类
        class OptimizedStockAnalyzer:
            def __init__(self, max_workers=8):
                self.max_workers = max_workers

            def scan_market_signals_optimized(self, date):
                from utils.logger import logger
                logger.warning("优化分析器不可用，请检查导入")
                return []

class FastIndicatorCalculator:
    """快速技术指标计算器"""

    def __init__(self):
        self._cache = {}
        self._cache_limit = 500

    @lru_cache(maxsize=100)
    def calculate_fast_indicators(self, code: str, df_hash: str, close_data: tuple,
                                high_data: tuple, low_data: tuple, volume_data: tuple) -> dict:
        """使用缓存的快速指标计算"""
        try:
            close = np.array(close_data)
            high = np.array(high_data)
            low = np.array(low_data)
            volume = np.array(volume_data)

            indicators = {}

            # 快速MA计算
            if len(close) >= 30:
                ma5 = talib.SMA(close, timeperiod=5)[-1]
                ma10 = talib.SMA(close, timeperiod=10)[-1]
                ma20 = talib.SMA(close, timeperiod=20)[-1]
                ma30 = talib.SMA(close, timeperiod=30)[-1]

                indicators['ma_bullish'] = close[-1] > ma5 > ma10 > ma20 > ma30
                indicators['ma_signal'] = 1 if indicators['ma_bullish'] else -1

            # 快速MACD计算
            if len(close) >= 26:
                dif, dea, hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
                if len(dif) > 1 and not np.isnan(dif[-1]) and not np.isnan(dea[-1]):
                    if dif[-1] > dea[-1] and dif[-2] <= dea[-2]:
                        indicators['macd_signal'] = 1  # 金叉
                    elif dif[-1] < dea[-1] and dif[-2] >= dea[-2]:
                        indicators['macd_signal'] = -1  # 死叉
                    else:
                        indicators['macd_signal'] = 0

            # 快速RSI计算
            if len(close) >= 14:
                rsi = talib.RSI(close, timeperiod=14)[-1]
                if not np.isnan(rsi):
                    if rsi < 30:
                        indicators['rsi_signal'] = 1  # 超卖
                    elif rsi > 70:
                        indicators['rsi_signal'] = -1  # 超买
                    else:
                        indicators['rsi_signal'] = 0

            # 快速成交量分析
            if len(volume) >= 10:
                volume_ma5 = talib.SMA(volume, timeperiod=5)[-1]
                if not np.isnan(volume_ma5) and volume_ma5 > 0:
                    volume_ratio = volume[-1] / volume_ma5
                    if volume_ratio > 2.0:
                        indicators['volume_signal'] = 1  # 放量
                    elif volume_ratio < 0.5:
                        indicators['volume_signal'] = -1  # 缩量
                    else:
                        indicators['volume_signal'] = 0

            return indicators

        except Exception as e:
            logger.error(f"快速指标计算失败: {e}")
            return {}

    def get_df_hash(self, df: pd.DataFrame) -> str:
        """获取DataFrame的简单哈希"""
        try:
            if len(df) > 0:
                return f"{len(df)}_{df.iloc[-1]['close']}_{df.iloc[-1]['volume']}"
            return "empty"
        except:
            return "error"


class BatchProcessor:
    """批量处理器"""

    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or min(mp.cpu_count(), 6)
        self.fast_calculator = FastIndicatorCalculator()

    def process_stocks_batch(self, stock_codes: list, date: str, analyzer) -> list:
        """批量处理股票"""
        try:
            logger.info(f"开始批量处理 {len(stock_codes)} 只股票，使用 {self.max_workers} 个线程")

            # 预加载数据
            start_date = ago_day_timestr(60, '%Y%m%d')
            all_data = fetch_all_history(begin=start_date, end=date)

            if all_data.empty:
                logger.warning("没有获取到历史数据")
                return []

            # 按股票分组
            stock_data = {}
            for code in stock_codes:
                code_data = all_data[all_data['code'] == code].copy()
                if not code_data.empty and len(code_data) >= 10:
                    code_data = code_data.sort_values('tradedate', ascending=True)
                    stock_data[code] = code_data

            logger.info(f"预加载完成，获取到 {len(stock_data)} 只股票的数据")

            # 分批处理
            batch_size = max(50, len(stock_codes) // self.max_workers)
            batches = [stock_codes[i:i + batch_size] for i in range(0, len(stock_codes), batch_size)]

            results = []

            # 使用线程池
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []

                for batch in batches:
                    future = executor.submit(self._process_batch, batch, stock_data, analyzer)
                    futures.append(future)

                for future in futures:
                    try:
                        batch_results = future.result(timeout=300)
                        results.extend(batch_results)
                    except Exception as e:
                        logger.error(f"批次处理失败: {e}")

            logger.info(f"批量处理完成，成功处理 {len(results)} 只股票")
            return results

        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return []

    def _process_batch(self, stock_codes: list, stock_data: dict, analyzer) -> list:
        """处理单个批次"""
        results = []

        for code in stock_codes:
            try:
                df = stock_data.get(code)
                if df is None or df.empty:
                    continue

                # 快速计算指标
                df_hash = self.fast_calculator.get_df_hash(df)
                close_data = tuple(df['close'].astype(float).values)
                high_data = tuple(df['high'].astype(float).values)
                low_data = tuple(df['low'].astype(float).values)
                volume_data = tuple(df['volume'].astype(float).values)

                indicators = self.fast_calculator.calculate_fast_indicators(
                    code, df_hash, close_data, high_data, low_data, volume_data
                )

                # 快速评分
                score = self._calculate_fast_score(code, df, indicators)

                # 同时调用旧分析器来填充 quant_stock_list
                try:
                    if hasattr(analyzer, 'stock_old_analyzer'):
                        old_analysis = analyzer.stock_old_analyzer.analyze_stock(code, df)
                        # 注意：quant_stock_list 在 analyze_stock 方法内部被填充
                except Exception as e:
                    logger.debug(f"旧分析器处理股票 {code} 失败: {e}")

                if score:
                    results.append(score)

            except Exception as e:
                logger.warning(f"处理股票 {code} 失败: {e}")
                continue

        return results

    def _calculate_fast_score(self, code: str, df: pd.DataFrame, indicators: dict) -> dict:
        """快速评分计算"""
        try:
            # 技术面评分
            technical_score = 50.0
            technical_score += indicators.get('ma_signal', 0) * 20
            technical_score += indicators.get('macd_signal', 0) * 15
            technical_score += indicators.get('rsi_signal', 0) * 10
            technical_score += indicators.get('volume_signal', 0) * 5

            # 其他维度简化评分
            fundamental_score = 50.0
            sentiment_score = 50.0 + (5 if code.startswith('30') else 0)  # 创业板加分

            # 资金面评分
            liquidity_score = 50.0
            if len(df) >= 10:
                recent_volume = df['volume'].iloc[-5:].mean()
                avg_volume = df['volume'].iloc[-20:].mean()
                if avg_volume > 0:
                    volume_ratio = recent_volume / avg_volume
                    if volume_ratio > 1.5:
                        liquidity_score += 15
                    elif volume_ratio > 1.2:
                        liquidity_score += 10

            # 综合评分
            comprehensive_score = (
                technical_score * 0.6 +
                fundamental_score * 0.0 +
                sentiment_score * 0.2 +
                liquidity_score * 0.2
            )

            return {
                'code': code,
                'technical_score': max(0, min(100, technical_score)),
                'fundamental_score': fundamental_score,
                'sentiment_score': max(0, min(100, sentiment_score)),
                'liquidity_score': max(0, min(100, liquidity_score)),
                'comprehensive_score': max(0, min(100, comprehensive_score)),
                'current_price': float(df.iloc[-1]['close']),
                'last_update': df.iloc[-1]['tradedate']
            }

        except Exception as e:
            logger.error(f"快速评分计算失败: {e}")
            return None


class SignalType(Enum):
    """信号类型枚举"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    STRONG_BUY = "STRONG_BUY"
    STRONG_SELL = "STRONG_SELL"

class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"

@dataclass
class TradingSignal:
    """交易信号数据类"""
    date: str
    signal_type: SignalType
    confidence: float  # 置信度 0-1
    strength: float    # 信号强度 0-100
    price: float
    volume: float
    indicators: Dict[str, Any]
    risk_level: RiskLevel
    reason: str
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

@dataclass
class StockOldAnalysis:
    """股票旧评分体系"""
    code: str
    limit: int
    ret10: float
    ret20: float
    ret100: float
    momentum: bool
    isBottomInversion: bool
    decisionPercent: float
    isTup: bool
    isHeavyVolume: bool
    macd: float
    kline: str
    ret: float
    md: float
    alpha: float
    beta: float
    emv: bool
    score1: float
    score2: float
    score3: float
    score4: float
    ma: bool
    max30: bool
    # 新增策略字段
    strongTrend: bool = False
    breakoutPlatform: bool = False
    bigDivergence: bool = False


@dataclass
class StockAnalysis:
    """股票分析结果"""
    code: str
    name: str
    current_price: float
    signals: List[TradingSignal]
    technical_score: float  # 技术面评分 0-100
    fundamental_score: float  # 基本面评分 0-100
    sentiment_score: float  # 风口面评分 0-100
    liquidity_score: float  # 资金面评分 0-100
    market_score: float  # 市场面评分 0-100
    risk_score: float  # 风险面评分 0-100
    macro_score: float  # 宏观面评分 0-100
    behavioral_score: float  # 行为面评分 0-100
    comprehensive_score: float  # 综合评分 0-100
    risk_level: RiskLevel
    recommendation: str
    last_update: str
    old_analysis: StockOldAnalysis

@dataclass
class FundamentalAnalysis:
    """基本面分析结果"""
    valuation_score: float  # 估值评分 0-100
    financial_score: float  # 财务评分 0-100
    growth_score: float     # 成长性评分 0-100
    profitability_score: float  # 盈利能力评分 0-100
    overall_score: float    # 综合评分 0-100
    details: Dict[str, Any]  # 详细数据

@dataclass
class SentimentAnalysis:
    """风口面分析结果"""
    industry_hot_score: float  # 行业热度评分 0-100
    policy_support_score: float  # 政策支持评分 0-100
    market_attention_score: float  # 市场关注度评分 0-100
    news_sentiment_score: float  # 新闻情感评分 0-100
    overall_score: float    # 综合评分 0-100
    details: Dict[str, Any]  # 详细数据

@dataclass
class LiquidityAnalysis:
    """资金面分析结果"""
    turnover_score: float  # 换手率评分 0-100
    volume_score: float  # 成交量评分 0-100
    north_money_score: float  # 北向资金评分 0-100
    margin_score: float  # 融资融券评分 0-100
    institutional_score: float  # 机构资金评分 0-100
    overall_score: float  # 综合评分 0-100
    details: Dict[str, Any]  # 详细数据

@dataclass
class MarketAnalysis:
    """市场面分析结果"""
    market_sentiment_score: float  # 市场情绪评分 0-100
    sector_rotation_score: float  # 板块轮动评分 0-100
    market_structure_score: float  # 市场结构评分 0-100
    market_breadth_score: float  # 市场宽度评分 0-100
    overall_score: float  # 综合评分 0-100
    details: Dict[str, Any]  # 详细数据

@dataclass
class RiskAnalysis:
    """风险面分析结果"""
    volatility_score: float  # 波动性评分 0-100
    liquidity_risk_score: float  # 流动性风险评分 0-100
    systematic_risk_score: float  # 系统性风险评分 0-100
    concentration_risk_score: float  # 集中度风险评分 0-100
    overall_score: float  # 综合评分 0-100
    details: Dict[str, Any]  # 详细数据

@dataclass
class MacroAnalysis:
    """宏观面分析结果"""
    interest_rate_score: float  # 利率环境评分 0-100
    inflation_score: float  # 通胀预期评分 0-100
    policy_score: float  # 政策环境评分 0-100
    economic_growth_score: float  # 经济增长评分 0-100
    overall_score: float  # 综合评分 0-100
    details: Dict[str, Any]  # 详细数据

@dataclass
class BehavioralAnalysis:
    """行为面分析结果"""
    herding_score: float  # 羊群效应评分 0-100
    overreaction_score: float  # 过度反应评分 0-100
    anchoring_score: float  # 锚定效应评分 0-100
    disposition_score: float  # 处置效应评分 0-100
    overall_score: float  # 综合评分 0-100
    details: Dict[str, Any]  # 详细数据

# 计算量化录入收益
def cal_quant_rate(df, df_quant):
    """
    计算量化录入收益

    :param df: 股票数据DataFrame
    :param df_quant: 量化数据DataFrame
    """
    try:
        if df_quant.empty or df.empty:
            return

        for idex, q in df_quant.iterrows():
            percent = 0
            daylast = df.iloc[-1]

            # 查找交易日数据
            if 'tradedate' in df.columns:
                daytrade = df[df['tradedate'] == q.date]
            else:
                # 如果没有tradedate字段，使用date字段
                daytrade = df[df['date'] == q.date]

            if not daytrade.empty and daylast.close > 0:
                day1 = daytrade.iloc[0]
                if day1.close > 0:
                    percent = round((daylast.close - day1.close) / day1.close, 4)

                    with db_manager.get_session() as session:
                        new_stock = StockQuant(
                            code=q.code,
                            date=q.date,
                            rate=float(percent)
                        )
                        session.merge(new_stock)
                        session.commit()

    except Exception as e:
        logger.error(f"计算量化收益失败: {e}")

def save_sql(name, desc, arr):
    """
    保存量化选股结果到数据库

    :param name: 策略名称
    :param desc: 策略描述
    :param arr: 股票代码列表
    """
    try:
        if not arr:
            logger.warning(f"策略 {name} 没有选出股票")
            return

        logger.info(f"保存策略 {name}: {len(arr)} 只股票")

        with db_manager.get_session() as session:
            # 保存到QuantList表
            item = {
                'name': name,
                'desc': desc,
                'list': (',').join(arr)
            }
            insert_stmt = insert(QuantList).values(**item)
            on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
            session.execute(on_duplicate_key_stmt)

            today = time.strftime('%Y%m%d', time.localtime(time.time()))

            # 确保交易日录入
            if is_trade_date(today):
                # 批量插入StockQuant记录
                stock_quant_records = []
                for stock in arr:
                    stock_quant_records.append({
                        'code': stock,
                        'reason': name,
                        'date': today
                    })

                # 批量插入
                if stock_quant_records:
                    for record in stock_quant_records:
                        insert_stmt = insert(StockQuant).values(**record)
                        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**record)
                        session.execute(on_duplicate_key_stmt)

            session.commit()

    except Exception as e:
        logger.error(f"保存策略 {name} 失败: {e}")


# 全局变量声明
bs = None
session = None
stocks = None
klines = None
start_date = None
end_date = None
decision_date = None
average_amount = None
average_value = None
quantId = None
quantRaw = None
hs300 = None
sz50 = None
cy50 = None
zz500 = None
kc50 = None
ret_max = None
df_all = None
df_quant_stock = None
df_source = None
df_stock = None
stock_trend_dict = {}
quant_stock_list = []

@monitor_performance
def initialize_system():
    """
    初始化量化分析系统

    :return: 是否成功
    """
    global bs, session, stocks, klines, start_date, end_date, decision_date
    global average_amount, average_value, quantId, quantRaw, hs300, sz50, cy50
    global zz500, kc50, ret_max, df_all, df_quant_stock, df_source, df_stock

    try:
        logger.info("开始初始化量化分析系统...")

        # 初始化基础组件
        bs = Baostock()
        session = DBSession()

        # 获取基础配置
        stocks = fetch_all_stock(mark=False)[:50]
        klines = fetch_all_kline()
        start_date, end_date, decision_date, average_amount, average_value, quantId = fetch_quant()
        quantRaw = fetch_quant_raw()

        logger.info(f'统计时间：{start_date} - {end_date}, 决断时间点：{decision_date}')

        # 获取指数数据
        logger.info("加载指数数据...")

        def safe_get_index_data(index_code, index_name):
            """安全获取指数数据"""
            try:
                data = bs.query_history_data(index_code, start_date=start_date, end_date=end_date)
                if data is None or data.empty:
                    logger.warning(f"指数 {index_name} ({index_code}) 无历史数据")
                    return pd.DataFrame(columns=['date', 'close'])

                if 'date' not in data.columns or 'close' not in data.columns:
                    logger.warning(f"指数 {index_name} ({index_code}) 数据格式异常: {data.columns.tolist()}")
                    return pd.DataFrame(columns=['date', 'close'])

                result = data[['date','close']].set_index('date').sort_index().reset_index()
                logger.debug(f"成功加载指数 {index_name}: {len(result)} 条记录")
                return result

            except Exception as e:
                logger.error(f"获取指数 {index_name} ({index_code}) 数据失败: {e}")
                return pd.DataFrame(columns=['date', 'close'])

        # 沪深300
        hs300 = safe_get_index_data('sh.000300', '沪深300')

        # 上证50
        sz50 = safe_get_index_data('sh.000016', '上证50')

        # 创业板50
        cy50 = safe_get_index_data('sz.399673', '创业板50')

        # 中证500
        zz500 = safe_get_index_data('sh.000905', '中证500')

        # 科创50
        kc50 = safe_get_index_data('sh.000688', '科创50')

        # 计算动量基准
        index_momentums = [quantRaw.momentumRet]

        # 安全计算各指数动量
        index_data_list = [
            (hs300, '沪深300'),
            (sz50, '上证50'),
            (cy50, '创业板50'),
            (zz500, '中证500'),
            (kc50, '科创50')
        ]

        for index_data, index_name in index_data_list:
            try:
                if not index_data.empty and len(index_data) > quantRaw.momentumDay:
                    momentum = cal_price_momentum(index_data, quantRaw.momentumDay)
                    index_momentums.append(momentum)
                    logger.debug(f"{index_name} 动量: {momentum}")
                else:
                    logger.warning(f"{index_name} 数据不足，跳过动量计算")
            except Exception as e:
                logger.warning(f"计算 {index_name} 动量失败: {e}")

        ret_max = max(index_momentums) if index_momentums else 0

        logger.info(f"动量基准设定为: {ret_max}")


        # 获取股票趋势热度数据
        try:
            sql = 'SELECT SUM(score) as score, code FROM stocktrend GROUP BY code LIMIT 1000'
            with engine.connect() as conn:
                result = conn.execute(sql)
                for row in result:
                    stock_trend_dict[row['code']] = row['score']
            logger.info(f"加载股票热度数据: {len(stock_trend_dict)} 只股票")
        except Exception as e:
            logger.warning(f"加载股票热度数据失败: {e}")

        # 获取股票数据
        logger.info("加载股票历史数据...")
        df_all = fetch_all_history(begin=start_date, end=end_date)
        df_quant_stock = fetch_quant_stock()
        df_source = fetch_all_stock_source()
        df_stock = fetch_all_stock_data()

        logger.info("✅ 系统初始化完成")
        return True

    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False


# 旧方法分析处理器
class StockOldAnalyzer:
    """旧方法分析处理器"""
    def __init__(self):
        self.settings = get_settings()

    def analyze_stock(self, code: str, df: pd.DataFrame) -> Dict[str, Any]:
        """分析股票"""
        try:
            logger.debug(f"开始分析股票 {code} 旧方法")

            # 检查全局变量是否已初始化
            global df_quant_stock, decision_date, quantRaw, ret_max
            if df_quant_stock is None:
                logger.warning(f"df_quant_stock 未初始化，股票 {code} 分析可能受影响")
                df_quant = pd.DataFrame()
            else:
                df_quant = df_quant_stock[df_quant_stock['code'] == code].copy()

            if df.empty:
                logger.warning(f"股票 {code} 没有历史数据")
                return None

            df = df.sort_values('tradedate', ascending=True)

            # 计算均线
            ma_list = [5, 10, 30]
            for ma in ma_list:
                df['ma' + str(ma)] = df['close'].rolling(ma).mean()

            # reverse
            df = df.reset_index()
            df.dropna() 

            # 获取从决断时间点的数据
            df_interval = df[df['date'] >= decision_date]

            # 初始化默认值
            result = {
                'code': code,
                'limit': 0,
                'ret10': 0,
                'ret20': 0,
                'ret100': 0,
                'momentum': False,
                'isBottomInversion': False,
                'decisionPercent': 0,
                'isTup': False,
                'isHeavyVolume': False,
                'macd': 0,
                'kline': '',
                'ret': 0,
                'md': 0,
                'alpha': 0,
                'beta': 0,
                'emv': False,
                'score1': 0,
                'score2': 0,
                'score3': 0,
                'score4': 0,
                'ma': False,
                'max30': False,
                # 新增策略默认值
                'strongTrend': False,
                'breakoutPlatform': False,
                'bigDivergence': False,
                'fundAccumulation': False
            }

            # 选取大于15个交易日的股票，排除新股
            if not df.empty and len(df) > 15 and not df_interval.empty:

                # 连板计算
                result['limit'] = self._cal_limit_num(df)

                # 10,20日收益率
                result['ret10'] = cal_price_momentum(df, 10)
                result['ret20'] = cal_price_momentum(df, 20)

                # 最低点到目前的收益率
                result['ret100'] = self._cal_bottom_return(df)

                # 动量
                result['momentum'] = self._cal_momentum(df)

                # 底部反转
                result['isBottomInversion'] = self._cal_bottom_inversion(df)

                # 决断日收益率
                result['decisionPercent'] = self._cal_decision_date_return(df_interval)

                # 是否三连阳
                result['isTup'] = self._cal_isTup(df)

                # 放量实体阳
                result['isHeavyVolume'] =  self._cal_isHeavyVolume(df)

                # CAPM
                result['ret'], result['md'], result['alpha'], result['beta'] = self._cal_CAPM(df)

                # k线指标
                result['kline'] = self._cal_k(df)
                result['emv'] = self._cal_emv(df)

                # macd背离
                result['macd'] = self._cal_macd(df)

                # 上穿均线
                result['ma'] = self._cal_ma(df)

                result['max30'] = self._cal_high_max(df)

                # 计算量化收录日到目前的收益率
                cal_quant_rate(df, df_quant)

                # 新增策略计算
                result['strongTrend'] = self._cal_strong_trend(df)
                result['breakoutPlatform'] = self._cal_breakout_platform(df)
                result['bigDivergence'] = self._cal_big_divergence(df)
                result['fundAccumulation'] = self._cal_fund_accumulation(df)

            if result is not None:
                try:
                    # 确保所有字段都存在且格式正确
                    stock_data = [
                        code,
                        result['isBottomInversion'],
                        result['isHeavyVolume'],
                        result['isTup'],
                        result['macd'],
                        result['ma'],
                        result['emv'],
                        result['strongTrend'],
                        result['breakoutPlatform'],
                        result['bigDivergence'],
                        result['fundAccumulation']
                    ]

                    # 验证数据长度
                    if len(stock_data) == 11:
                        quant_stock_list.append(stock_data)
                    else:
                        logger.warning(f"股票 {code} 数据格式错误，长度: {len(stock_data)}")
                except KeyError as e:
                    logger.warning(f"股票 {code} 缺少字段: {e}")
                except Exception as e:
                    logger.warning(f"股票 {code} 数据处理失败: {e}")

            return StockOldAnalysis(
                code=code,
                limit=result['limit'],
                isBottomInversion=result['isBottomInversion'],
                isHeavyVolume=result['isHeavyVolume'],
                isTup=result['isTup'],
                ret=float(result['ret']),
                md=float(result['md']),
                alpha=float(result['alpha']),
                beta=float(result['beta']),
                momentum=result['momentum'],
                decisionPercent=float(result['decisionPercent']),
                ret10=float(result['ret10']),
                ret20=float(result['ret20']),
                ret100=float(result['ret100']),
                kline=result['kline'],
                emv=result['emv'],
                macd=result['macd'],
                ma=result['ma'],
                max30=result['max30'],
                score1=result['score1'],
                score2=result['score2'],
                score3=result['score3'],
                score4=result['score4'],
                # 新增策略字段, 数据库中没有
                # strongTrend=result['strongTrend'],
                # breakoutPlatform=result['breakoutPlatform'],
                # bigDivergence=result['bigDivergence']
            )

        except Exception as e:
            logger.error(f"分析股票 {code} 旧方法失败: {e}")
            return None              

    def _cal_k(self, df):
        """
        TA-Lib K线模式识别

        :param df: 股票数据DataFrame
        :return: 识别到的K线模式，逗号分隔
        """
        try:
            if len(df) < 10:  # 需要足够的数据
                return ''

            o, hi, l, c = map((lambda x : np.array(df[x], dtype=float)),['open','high','low','close'])
            k = []

            for method in klines:
                try:
                    if hasattr(talib, method):
                        r = getattr(talib, method)(o, hi, l, c)
                        if len(r) > 0 and r[-1] > 0:
                            k.append(method)
                except Exception:
                    continue  # 跳过有问题的指标

            return ','.join(k)

        except Exception as e:
            logger.error(f"K线模式识别失败: {e}")
            return ''

    def _TEMV(self, data, fasttimeperiod, lasttimeperiod):
        """
        计算EMV技术指标

        :param data: 股票数据DataFrame
        :param fasttimeperiod: 快速周期
        :param lasttimeperiod: 慢速周期
        :return: EMV, MAEMV, SubEMV
        """
        try:
            temp = data.loc[:,['date','open','high','low','close','volume']].copy()
            temp['sub'] = 2

            emFront = talib.DIV(talib.ADD(temp['high'],temp['low']),temp['sub'])
            emFrontSub = talib.DIV(talib.ADD(temp['high'].shift(1),temp['low'].shift(1)),temp['sub'])
            emEnd = talib.DIV(talib.SUB(temp['high'],temp['low']),temp['volume'])
            em = talib.SUB(emFront, emFrontSub)*emEnd
            em = em.dropna()

            EMV = talib.SMA(em, fasttimeperiod)
            MAEMV = talib.SMA(EMV, lasttimeperiod)
            SubEMV = talib.SUB(EMV, MAEMV)

            return EMV, MAEMV, SubEMV

        except Exception as e:
            logger.error(f"TEMV计算失败: {e}")
            return pd.Series(), pd.Series(), pd.Series()

    def _cal_emv(self, df):
        """
        优化的EMV技术指标信号

        优化点：
        1. 多重EMV确认
        2. 趋势强度判断
        3. 背离识别
        4. 量价效率评估

        :param df: 股票数据DataFrame
        :return: EMV信号
        """
        try:
            if len(df) < 30:
                return False

            # 计算不同周期的EMV
            emv_5_10, maemv_5_10, subemv_5_10 = self._TEMV(df, 5, 10)
            emv_10_20, maemv_10_20, subemv_10_20 = self._TEMV(df, 10, 20)

            if (len(emv_5_10) == 0 or len(maemv_5_10) == 0 or
                len(emv_10_20) == 0 or len(maemv_10_20) == 0):
                return False

            # 检查数据有效性
            if (pd.isna(emv_5_10.iloc[-1]) or pd.isna(maemv_5_10.iloc[-1]) or
                pd.isna(emv_10_20.iloc[-1]) or pd.isna(maemv_10_20.iloc[-1])):
                return False

            # 条件1: 短期EMV信号
            short_signal = (emv_5_10.iloc[-1] > 0 and maemv_5_10.iloc[-1] > 0 and
                          emv_5_10.iloc[-1] > maemv_5_10.iloc[-1])

            # 条件2: 长期EMV确认
            long_signal = (emv_10_20.iloc[-1] > maemv_10_20.iloc[-1] or
                         emv_10_20.iloc[-1] > 0)

            # 条件3: EMV趋势强度
            emv_strength = True
            try:
                if len(emv_5_10) >= 5:
                    # EMV连续上升
                    emv_trend = (emv_5_10.iloc[-1] > emv_5_10.iloc[-2] > emv_5_10.iloc[-3])
                    # 或者从负值快速转正
                    emv_reversal = (emv_5_10.iloc[-1] > 0 and emv_5_10.iloc[-3] < 0)
                    emv_strength = emv_trend or emv_reversal
            except:
                pass

            # 条件4: 价格配合
            price_confirmation = True
            try:
                current_price = df['close'].iloc[-1]
                prev_price = df['close'].iloc[-3]
                price_up = current_price > prev_price

                # 价格突破前期高点
                recent_high = df['high'].iloc[-10:-1].max()
                price_breakthrough = current_price > recent_high * 0.98

                price_confirmation = price_up and price_breakthrough
            except:
                pass

            # 条件5: 成交量效率
            volume_efficiency = True
            try:
                # EMV反映的是价格变动的容易程度
                # 正值且上升表示价格上涨容易（成本低）
                if len(subemv_5_10) >= 3:
                    # SubEMV（EMV-MAEMV）转正或持续为正
                    subemv_positive = (subemv_5_10.iloc[-1] > 0 and
                                     subemv_5_10.iloc[-1] > subemv_5_10.iloc[-2])
                    volume_efficiency = subemv_positive
            except:
                pass

            # 条件6: 避免超买
            avoid_overbought = True
            try:
                if len(emv_5_10) >= 10:
                    # EMV不能过度偏离均值
                    emv_mean = emv_5_10.iloc[-10:].mean()
                    emv_std = emv_5_10.iloc[-10:].std()
                    current_emv = emv_5_10.iloc[-1]

                    if emv_std > 0:
                        z_score = (current_emv - emv_mean) / emv_std
                        avoid_overbought = z_score < 2.0  # 不超过2个标准差
            except:
                pass

            # 综合判断
            return (short_signal and long_signal and emv_strength and
                   price_confirmation and volume_efficiency and avoid_overbought)

        except Exception as e:
            logger.error(f"EMV计算失败: {e}")
            return False

    def _cal_limit_num(self, df):
        """
        计算涨停连板数

        :param df: 股票数据DataFrame
        :return: 连续涨停天数
        """
        try:
            if len(df) < 1:
                return 0

            df_reversed = df.iloc[::-1].reset_index()
            limit = 0

            # 统计连板数
            for stock in df_reversed.itertuples():
                if isLimit(stock):
                    limit = limit + 1
                else:
                    break

            return limit

        except Exception as e:
            logger.error(f"计算连板数失败: {e}")
            return 0

    def _cal_bottom_return(self, df):
        """
        时间段内最低点到目前的收益率

        :param df: 股票数据DataFrame
        :return: 从最低点到当前的收益率
        """
        try:
            if len(df) < 2:
                return 0

            daylast = df.iloc[-1]
            day1 = df.sort_values(by="close").iloc[0]

            if day1.close <= 0:
                return 0

            percent = round((daylast.close - day1.close) / day1.close, 4)
            return percent

        except Exception as e:
            logger.error(f"计算底部收益率失败: {e}")
            return 0

    def _cal_decision_date_return(self, df):
        """
        计算决断日收益率

        :param df: 股票数据DataFrame
        :return: 从开始到结束的收益率
        """
        try:
            if len(df) < 2:
                return 0

            daylast = df.iloc[-1]
            day1 = df.iloc[0]

            if day1.close <= 0:
                return 0

            percent = round((daylast.close - day1.close) / day1.close, 4)
            return percent

        except Exception as e:
            logger.error(f"计算决断日收益率失败: {e}")
            return 0

    def _cal_isTup(self, df):
        """
        优化的三连阳算法：连续三日上涨模式

        优化点：
        1. 灵活的涨幅要求
        2. 成交量递增确认
        3. 均线支撑判断
        4. 避免假突破

        :param df: 股票数据DataFrame
        :return: 是否为三连阳
        """
        try:
            if len(df) < 5:
                return False

            df_clean = df.dropna(axis=0)
            if len(df_clean) < 5:
                return False

            day1 = df_clean.iloc[-1]  # 最新
            day2 = df_clean.iloc[-2]  # 前一天
            day3 = df_clean.iloc[-3]  # 前两天
            day0 = df_clean.iloc[-4]  # 前三天（用于对比）

            # 条件1: 连续三天都是阳线
            is_three_yang = (day1.close > day1.open and
                           day2.close > day2.open and
                           day3.close > day3.open)

            if not is_three_yang:
                return False

            # 计算涨幅
            def calc_pct_change(day):
                if hasattr(day, 'pctChg') and not pd.isna(day.pctChg):
                    return day.pctChg
                else:
                    return (day.close - day.open) / day.open * 100

            pct1 = calc_pct_change(day1)
            pct2 = calc_pct_change(day2)
            pct3 = calc_pct_change(day3)

            # 条件2: 涨幅要求 - 更灵活的标准
            # 每天涨幅在1%-8%之间，总涨幅在4%-15%之间
            daily_gains_valid = (1 <= pct1 <= 8 and 1 <= pct2 <= 8 and 1 <= pct3 <= 8)
            total_gain = ((day1.close - day3.open) / day3.open) * 100
            total_gain_valid = 4 <= total_gain <= 15

            if not (daily_gains_valid and total_gain_valid):
                return False

            # 条件3: 成交量逐步放大或保持活跃
            volume_trend = True
            try:
                # 至少有一天成交量明显放大
                avg_volume_before = df_clean['volume'].iloc[-7:-4].mean()
                recent_volumes = [day3.volume, day2.volume, day1.volume]
                max_recent_volume = max(recent_volumes)

                if avg_volume_before > 0:
                    volume_amplification = max_recent_volume / avg_volume_before
                    volume_trend = volume_amplification >= 1.2
            except:
                pass

            # 条件4: 价格逐步抬升
            price_progression = (day1.close > day2.close > day3.close and
                               day1.low >= day2.low * 0.98 and  # 低点不能大幅下移
                               day2.low >= day3.low * 0.98)

            # 条件5: 均线支撑
            ma_support = True
            try:
                close = df_clean['close'].astype(float)
                ma5 = close.rolling(5).mean()
                if len(ma5) >= 3:
                    # 三连阳期间都在5日线之上或接近
                    ma5_current = ma5.iloc[-1]
                    ma_support = (day1.close >= ma5_current * 0.98 and
                                day2.close >= ma5.iloc[-2] * 0.98 and
                                day3.close >= ma5.iloc[-3] * 0.98)
            except:
                pass

            return price_progression and volume_trend and ma_support

        except Exception as e:
            logger.error(f"计算三连阳失败: {e}")
            return False

    def _cal_momentum(self, df):
        """
        计算动量信号

        :param df: 股票数据DataFrame
        :return: 是否有动量信号
        """
        try:
            momentum = False
            ret = cal_price_momentum(df, quantRaw.momentumDay)
            daylast = df.iloc[-1]

            # 检查是否有amount字段，如果没有使用volume
            if 'amount' in df.columns:
                volume10 = df['amount'].rolling(10).mean().iloc[-1]
            else:
                volume10 = df['volume'].rolling(10).mean().iloc[-1] * daylast.close  # 估算成交额

            # 检查是否有ma10字段
            ma10_value = daylast.ma10 if hasattr(daylast, 'ma10') else daylast.close

            if ret >= ret_max and daylast.close >= ma10_value and volume10 > 1 * 100000000:
                momentum = True

            return momentum

        except Exception as e:
            logger.error(f"计算动量信号失败: {e}")
            return False

    def _cal_isHeavyVolume(self, df):
        """
        优化的放量实体阳算法：成交量是前5天的两倍，放量实体阳线

        优化点：
        1. 多时间周期成交量对比
        2. 实体大小和影线比例判断
        3. 价格突破确认
        4. 量价配合度评估

        :param df: 股票数据DataFrame
        :return: 是否为放量实体阳
        """
        try:
            if len(df) < 10:
                return False

            day1 = df.iloc[-1]

            # 条件1: 放量判断 - 多重确认
            # 与前5天平均成交量对比
            avg_volume_5 = df['volume'].iloc[-6:-1].mean()
            # 与前10天平均成交量对比
            avg_volume_10 = df['volume'].iloc[-11:-1].mean()

            volume_ratio_5 = day1.volume / avg_volume_5 if avg_volume_5 > 0 else 0
            volume_ratio_10 = day1.volume / avg_volume_10 if avg_volume_10 > 0 else 0

            # 放量标准：至少是前5天的2倍，且是前10天的1.5倍
            is_heavy_volume = volume_ratio_5 >= 2.0 and volume_ratio_10 >= 1.5

            if not is_heavy_volume:
                return False

            # 条件2: 实体阳线判断
            body_size = day1.close - day1.open
            total_range = day1.high - day1.low

            # 必须是阳线
            if body_size <= 0:
                return False

            # 实体占比要求（实体至少占总振幅的60%）
            body_ratio = body_size / total_range if total_range > 0 else 0
            if body_ratio < 0.6:
                return False

            # 条件3: 涨幅要求
            if hasattr(day1, 'pctChg'):
                pct_change = day1.pctChg
            else:
                pct_change = (day1.close - day1.open) / day1.open * 100

            # 涨幅在3%-9%之间（避免过度投机）
            if pct_change < 3 or pct_change > 9:
                return False

            # 条件4: 价格位置判断
            # 收盘价应该接近当日最高价
            close_to_high_ratio = (day1.close - day1.low) / (day1.high - day1.low) if day1.high > day1.low else 0
            if close_to_high_ratio < 0.8:
                return False

            # 条件5: 突破前期阻力
            # 收盘价突破前5天最高价
            prev_high = df['high'].iloc[-6:-1].max()
            breakthrough = day1.close > prev_high

            return breakthrough

        except Exception as e:
            logger.error(f"计算放量实体阳失败: {e}")
            return False

    def _cal_bottom_inversion(self, df):
        """
        优化的底部反转算法：持续下跌后的放量大阳，均线上穿

        优化点：
        1. 增加多重确认条件
        2. 考虑价格位置和趋势
        3. 增强量价配合判断
        4. 添加均线支撑确认

        :param df: 股票数据DataFrame
        :return: 是否为底部反转
        """
        try:
            if len(df) < 60:
                return False

            # 获取最新数据
            daylast = df.iloc[-1]
            day_prev = df.iloc[-2]

            # 计算均线
            close = df['close'].astype(float)
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()
            ma20 = close.rolling(20).mean()

            # 条件1: 前期持续下跌（60日跌幅超过30%）
            momentum_60 = cal_price_momentum(df, 60)
            if momentum_60 >= -0.3:
                return False

            # 条件2: 当日放量大阳线
            isHeavyVolume = self._cal_isHeavyVolume(df)
            if not isHeavyVolume:
                return False

            # 条件3: 价格接近或突破关键均线
            current_ma5 = ma5.iloc[-1]
            current_ma10 = ma10.iloc[-1]
            prev_ma5 = ma5.iloc[-2]

            # 5日线开始上穿或接近10日线
            ma_signal = (current_ma5 >= prev_ma5 * 1.002 and  # 5日线上涨
                        abs(current_ma5 - current_ma10) / current_ma10 < 0.05)  # 接近10日线

            # 条件4: 成交量放大且价格突破
            volume_ratio = daylast.volume / df['volume'].iloc[-10:-1].mean()
            price_breakthrough = daylast.close > max(df['high'].iloc[-5:-1])

            # 条件5: RSI从超卖区域回升
            rsi_signal = True
            try:
                rsi = talib.RSI(close.values, timeperiod=14)
                if len(rsi) > 5:
                    current_rsi = rsi[-1]
                    prev_rsi = rsi[-2]
                    # RSI从30以下回升到35以上
                    rsi_signal = (prev_rsi < 35 and current_rsi > prev_rsi and current_rsi > 30)
            except:
                pass

            # 综合判断
            return (isHeavyVolume and ma_signal and
                   volume_ratio > 1.5 and price_breakthrough and rsi_signal)

        except Exception as e:
            logger.error(f"计算底部反转失败: {e}")
            return False

    def _max_drawdown(self, df):
        """
        最大回撤

        :param df: 价格序列
        :return: 最大回撤
        """
        try:
            if len(df) < 2:
                return 0

            md = ((df.cummax() - df) / df.cummax()).max()
            return round(md, 4)

        except Exception as e:
            logger.error(f"计算最大回撤失败: {e}")
            return 0

    def _cal_high_max(self, df, days=30):
        """
        计算收盘价是否为最高价

        :param df: 股票数据DataFrame
        :param days: 天数
        :return: 是否为最高价
        """
        try:
            if len(df) < days:
                return False

            if days >= 30:
                df_30 = df.iloc[-days:-1]
                max_30 = df_30['high'].max()
                last = df.iloc[-1]
                if last.close >= max_30:
                    return True

            return False

        except Exception as e:
            logger.error(f"计算最高价失败: {e}")
            return False

    def _cal_CAPM(self, df):
        """
        资本资产定价模型

        :param df: 股票数据DataFrame
        :return: (收益率, 最大回撤, alpha, beta)
        """
        try:
            if len(df) < 30:
                return 0, 0, 0, 0

            daylast = df.iloc[-1]
            dayfirst = df.iloc[0]

            if dayfirst.close <= 0:
                return 0, 0, 0, 0

            # 净值
            worth = round(daylast.close / dayfirst.close, 4)
            # 收益率
            ret = worth - 1
            # 最大回撤
            close = df['close']
            md = self._max_drawdown(close)

            # 如果 statsmodels 不可用，只返回基本指标
            if sm is None:
                return ret, md, 0, 0

            # 长度不一样，不计算
            if len(df) != len(hs300):
                return ret, md, 0, 0

            stock = df[['date','close']].copy()
            stock = stock.set_index('date')
            stock = stock.sort_index(ascending=True)
            stock = stock.reset_index()
            Ri_stock = np.log(stock['close']/stock['close'].shift(1))
            Ri_stock = Ri_stock.dropna()

            # beta，alpha
            Rm = np.log(hs300['close'] / hs300['close'].shift(1))
            Rm = Rm.dropna()

            if len(Ri_stock) != len(Rm) or len(Ri_stock) < 10:
                return ret, md, 0, 0

            Rm_add = sm.add_constant(Rm)   # 增加常数列
            model = sm.OLS(endog=Ri_stock, exog=Rm_add)  #计算股票收益率关于沪深300的线性回归模型
            result = model.fit()   # 拟合

            # 年化
            alpha = round(result.params.iloc[0] * 250, 4)
            beta = round(result.params.iloc[1], 4)

            return ret, md, alpha, beta

        except Exception as e:
            logger.error(f"CAPM计算失败: {e}")
            return 0, 0, 0, 0


    def _cal_macd(self, df):
        """
        优化的MACD底背离信号计算

        优化点：
        1. 识别底背离模式
        2. 多重时间周期确认
        3. 量价配合验证
        4. 强度评估

        :param df: 股票数据DataFrame
        :return: MACD信号 (1: 底背离买入, -1: 顶背离卖出, 0: 无信号)
        """
        try:
            if len(df) < 60:
                return 0

            close = df['close'].astype(float).values
            dif, dea, hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)

            # 去除NaN值
            valid_indices = ~(np.isnan(dif) | np.isnan(dea) | np.isnan(hist))
            if np.sum(valid_indices) < 30:
                return 0

            dif_clean = dif[valid_indices]
            dea_clean = dea[valid_indices]
            hist_clean = hist[valid_indices]
            close_clean = close[valid_indices]

            if len(dif_clean) < 30:
                return 0

            # 寻找底背离
            def find_bottom_divergence():
                # 寻找最近30天内的价格低点和MACD低点
                recent_period = 30
                if len(close_clean) < recent_period:
                    return False

                price_recent = close_clean[-recent_period:]
                dif_recent = dif_clean[-recent_period:]

                # 找到价格的两个低点
                price_lows = []
                dif_lows = []

                for i in range(5, len(price_recent) - 5):
                    # 价格低点：前后5天都比当天高
                    if (price_recent[i] == min(price_recent[i-5:i+6]) and
                        price_recent[i] < price_recent[i-1] and price_recent[i] < price_recent[i+1]):
                        price_lows.append((i, price_recent[i]))
                        dif_lows.append((i, dif_recent[i]))

                if len(price_lows) < 2:
                    return False

                # 取最近的两个低点
                price_low1 = price_lows[-2]
                price_low2 = price_lows[-1]
                dif_low1 = dif_lows[-2]
                dif_low2 = dif_lows[-1]

                # 底背离：价格创新低，但MACD不创新低
                price_divergence = price_low2[1] < price_low1[1]  # 价格新低
                macd_divergence = dif_low2[1] > dif_low1[1]       # MACD不创新低

                return price_divergence and macd_divergence

            # 检查当前是否有金叉信号
            def check_golden_cross():
                if len(dif_clean) < 3:
                    return False

                # 当前DIF上穿DEA
                current_cross = (dif_clean[-1] > dea_clean[-1] and
                               dif_clean[-2] <= dea_clean[-2])

                # MACD在零轴下方（底部区域）
                below_zero = dif_clean[-1] < 0 and dea_clean[-1] < 0

                # HIST柱状线由负转正
                hist_turning = (hist_clean[-1] > 0 and hist_clean[-2] <= 0)

                return current_cross and below_zero and hist_turning

            # 检查成交量配合
            def check_volume_confirmation():
                try:
                    if len(df) < 10:
                        return True  # 数据不足时不作为否决条件

                    recent_volume = df['volume'].iloc[-3:].mean()
                    prev_volume = df['volume'].iloc[-10:-3].mean()

                    return recent_volume > prev_volume * 1.1  # 成交量放大
                except:
                    return True

            # 综合判断
            has_bottom_divergence = find_bottom_divergence()
            has_golden_cross = check_golden_cross()
            volume_confirmed = check_volume_confirmation()

            if has_bottom_divergence and has_golden_cross and volume_confirmed:
                return -1  # 底背离买入信号（策略中用-1表示）

            # 检查简单的金叉信号作为备选
            if has_golden_cross and volume_confirmed:
                return -1

            return 0

        except Exception as e:
            logger.error(f"MACD计算失败: {e}")
            return 0

    def _cal_ma(self, df):
        """
        优化的均线多头排列算法：5日线上穿10日线和30日线

        优化点：
        1. 多重均线确认
        2. 均线斜率判断
        3. 成交量配合
        4. 价格位置验证

        :param df: 股票数据DataFrame
        :return: 是否有均线多头排列信号
        """
        try:
            if len(df) < 35:
                return False

            # 计算多条均线
            close = df['close'].astype(float)
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()
            ma20 = close.rolling(20).mean()
            ma30 = close.rolling(30).mean()

            if len(ma5) < 5 or len(ma10) < 5 or len(ma30) < 5:
                return False

            # 获取最新数据
            current_price = close.iloc[-1]
            current_ma5 = ma5.iloc[-1]
            current_ma10 = ma10.iloc[-1]
            current_ma20 = ma20.iloc[-1]
            current_ma30 = ma30.iloc[-1]

            prev_ma5 = ma5.iloc[-2]
            prev_ma10 = ma10.iloc[-2]
            prev_ma30 = ma30.iloc[-2]

            # 条件1: 当前多头排列
            current_bullish = (current_price > current_ma5 > current_ma10 > current_ma30)

            # 条件2: 5日线上穿确认（从下方穿越到上方）
            ma5_cross_ma10 = (current_ma5 > current_ma10 and prev_ma5 <= prev_ma10)
            ma5_cross_ma30 = (current_ma5 > current_ma30 and prev_ma5 <= prev_ma30)

            # 至少有一个上穿信号
            has_cross_signal = ma5_cross_ma10 or ma5_cross_ma30

            # 条件3: 均线斜率向上
            ma5_slope = (current_ma5 - ma5.iloc[-3]) / ma5.iloc[-3]
            ma10_slope = (current_ma10 - ma10.iloc[-3]) / ma10.iloc[-3]

            positive_slope = ma5_slope > 0.002 and ma10_slope > 0.001  # 均线向上

            # 条件4: 价格强势确认
            # 收盘价在5日线上方且距离适中
            price_above_ma5 = current_price > current_ma5
            price_distance = (current_price - current_ma5) / current_ma5
            reasonable_distance = 0 < price_distance < 0.05  # 不能距离太远

            # 条件5: 成交量配合
            volume_support = True
            try:
                recent_volume = df['volume'].iloc[-3:].mean()
                prev_volume = df['volume'].iloc[-8:-3].mean()
                volume_support = recent_volume > prev_volume * 1.1
            except:
                pass

            # 条件6: 避免假突破
            # 检查前期是否有明显的下跌趋势
            trend_reversal = True
            try:
                # 20日前的价格对比
                if len(close) > 20:
                    price_20_ago = close.iloc[-20]
                    recent_low = close.iloc[-10:].min()
                    # 从低位反弹
                    trend_reversal = recent_low < price_20_ago * 0.95
            except:
                pass

            # 综合判断
            return (current_bullish and has_cross_signal and positive_slope and
                   price_above_ma5 and reasonable_distance and volume_support)

        except Exception as e:
            logger.error(f"计算均线信号失败: {e}")
            return False

    def _cal_strong_trend(self, df):
        """
        强势趋势股策略：沿着5日线上涨（优化版）

        特征：
        1. 股价持续在5日均线之上运行
        2. 5日均线呈上升趋势，且有明确的上涨斜率
        3. 回调不破5日线
        4. 成交量配合
        5. 上涨斜率符合要求（避免选出过多股票）

        :param df: 股票数据DataFrame
        :return: 是否为强势趋势股
        """
        try:
            if len(df) < 30:  # 增加最小数据要求
                return False

            # 计算5日均线和10日均线
            close = df['close'].astype(float)
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()

            if len(ma5) < 15:
                return False

            # 条件1: 最近10天股价都在5日线之上或接近（更严格）
            recent_days = 10
            above_ma5_count = 0

            for i in range(recent_days):
                idx = -(i + 1)
                if idx < -len(close):
                    break

                price = close.iloc[idx]
                ma5_value = ma5.iloc[idx]

                # 允许轻微跌破（不超过1.5%，更严格）
                if price >= ma5_value * 0.985:
                    above_ma5_count += 1

            # 至少85%的时间在5日线之上（更严格）
            above_ma5_ratio = above_ma5_count / min(recent_days, len(close))
            if above_ma5_ratio < 0.85:
                return False

            # 条件2: 5日均线持续上升且斜率合理
            ma5_slope_periods = 8  # 增加观察期
            ma5_uptrend_count = 0
            slope_values = []

            for i in range(1, ma5_slope_periods + 1):
                if len(ma5) > i:
                    current_ma5 = ma5.iloc[-i]
                    prev_ma5 = ma5.iloc[-(i + 1)]
                    if current_ma5 > prev_ma5:
                        ma5_uptrend_count += 1
                        # 计算斜率（日涨幅）
                        slope = (current_ma5 - prev_ma5) / prev_ma5
                        slope_values.append(slope)

            # 5日线大部分时间上升（更严格）
            ma5_uptrend_ratio = ma5_uptrend_count / ma5_slope_periods
            if ma5_uptrend_ratio < 0.75:
                return False

            # 新增条件2.1: 上涨斜率要求
            if slope_values:
                avg_slope = sum(slope_values) / len(slope_values)
                # 5日均线日均涨幅要在0.3%-2%之间（避免过缓或过急）
                if avg_slope < 0.003 or avg_slope > 0.02:
                    return False

            # 新增条件2.2: 5日线相对10日线的位置
            if len(ma10) > 5:
                current_ma5 = ma5.iloc[-1]
                current_ma10 = ma10.iloc[-1]
                # 5日线要明显高于10日线
                if current_ma5 <= current_ma10 * 1.01:
                    return False

            # 条件3: 整体上升趋势确认（更严格）
            if len(close) > 15:
                current_price = close.iloc[-1]
                price_15_ago = close.iloc[-16]
                total_gain = (current_price - price_15_ago) / price_15_ago

                # 15天涨幅要在8%-25%之间（避免涨幅过小或过大）
                if total_gain < 0.08 or total_gain > 0.25:
                    return False

            # 新增条件3.1: 价格相对均线的强度
            current_price = close.iloc[-1]
            current_ma5 = ma5.iloc[-1]
            price_ma5_ratio = current_price / current_ma5
            # 价格要适度高于5日线，但不能过度偏离
            if price_ma5_ratio < 1.005 or price_ma5_ratio > 1.08:
                return False

            # 条件4: 成交量健康（更严格）
            volume_healthy = True
            try:
                recent_volume = df['volume'].iloc[-5:].mean()
                prev_volume = df['volume'].iloc[-10:-5].mean()
                long_avg_volume = df['volume'].iloc[-20:].mean()

                # 最近成交量要适度放大，但不能过度
                volume_ratio = recent_volume / prev_volume if prev_volume > 0 else 1
                long_volume_ratio = recent_volume / long_avg_volume if long_avg_volume > 0 else 1

                # 成交量要温和放大，避免暴涨暴跌
                volume_healthy = (0.8 <= volume_ratio <= 3.0 and
                                long_volume_ratio >= 1.1 and long_volume_ratio <= 2.5)
            except:
                volume_healthy = False

            # 条件5: 避免超涨（更严格的RSI要求）
            rsi_ok = True
            try:
                rsi = talib.RSI(close.values, timeperiod=14)
                if len(rsi) > 0 and not pd.isna(rsi[-1]):
                    current_rsi = rsi[-1]
                    # RSI要在合理区间，避免超买也避免过弱
                    rsi_ok = 45 <= current_rsi <= 75
            except:
                rsi_ok = False

            # 条件6: 价格形态稳健
            price_stable = True
            try:
                if len(df) >= 8:
                    recent_highs = df['high'].iloc[-8:]
                    recent_lows = df['low'].iloc[-8:]
                    recent_closes = df['close'].iloc[-8:]

                    # 检查是否有异常跳空（更严格）
                    for i in range(1, len(recent_highs)):
                        gap_up = recent_lows.iloc[i] > recent_highs.iloc[i-1] * 1.03
                        gap_down = recent_highs.iloc[i] < recent_lows.iloc[i-1] * 0.97

                        if gap_up or gap_down:
                            price_stable = False
                            break

                    # 新增：检查价格波动的稳定性
                    if price_stable:
                        daily_changes = []
                        for i in range(1, len(recent_closes)):
                            change = abs(recent_closes.iloc[i] - recent_closes.iloc[i-1]) / recent_closes.iloc[i-1]
                            daily_changes.append(change)

                        # 单日涨跌幅不能过大
                        max_daily_change = max(daily_changes) if daily_changes else 0
                        if max_daily_change > 0.08:  # 单日涨跌幅不超过8%
                            price_stable = False
            except:
                price_stable = False

            # 新增条件7: 趋势持续性验证
            trend_consistency = True
            try:
                if len(close) >= 20:
                    # 检查最近20天是否有明显的趋势一致性
                    ma5_20_ago = ma5.iloc[-20] if len(ma5) >= 20 else ma5.iloc[0]
                    current_ma5 = ma5.iloc[-1]

                    # 5日线20天总涨幅要合理
                    ma5_total_gain = (current_ma5 - ma5_20_ago) / ma5_20_ago
                    if ma5_total_gain < 0.06 or ma5_total_gain > 0.30:  # 6%-30%区间
                        trend_consistency = False
            except:
                trend_consistency = False

            # 新增条件8: 相对强度验证
            relative_strength = True
            try:
                if len(close) >= 30:
                    # 计算相对于自身历史的强度
                    recent_avg = close.iloc[-10:].mean()
                    historical_avg = close.iloc[-30:-10].mean()

                    strength_ratio = recent_avg / historical_avg
                    # 最近表现要明显强于历史平均
                    if strength_ratio < 1.08:  # 至少强8%
                        relative_strength = False
            except:
                relative_strength = False

            return (volume_healthy and rsi_ok and price_stable and
                   trend_consistency and relative_strength)

        except Exception as e:
            logger.error(f"计算强势趋势股失败: {e}")
            return False

    def _cal_breakout_platform(self, df):
        """
        脱离底部平台策略：成交量小碎步放大，行情刚起步

        特征：
        1. 前期有明显的底部平台整理
        2. 成交量逐步温和放大
        3. 价格开始突破平台上沿
        4. 早期突破信号

        :param df: 股票数据DataFrame
        :return: 是否为脱离底部平台
        """
        try:
            if len(df) < 30:
                return False

            close = df['close'].astype(float)
            high = df['high'].astype(float)
            low = df['low'].astype(float)
            volume = df['volume'].astype(float)

            # 条件1: 识别底部平台
            # 最近20天价格在相对窄幅区间内震荡
            platform_period = 20
            if len(close) < platform_period + 10:
                return False

            platform_data = close.iloc[-platform_period-10:-5]  # 平台期间数据
            platform_high = platform_data.max()
            platform_low = platform_data.min()
            platform_range = (platform_high - platform_low) / platform_low

            # 平台振幅不超过15%
            if platform_range > 0.15:
                return False

            # 条件2: 确认是底部（前期有下跌）
            if len(close) > 40:
                pre_platform_high = close.iloc[-50:-platform_period-10].max()
                decline_from_high = (platform_high - pre_platform_high) / pre_platform_high

                # 从前期高点下跌至少20%形成底部
                if decline_from_high > -0.2:
                    return False

            # 条件3: 成交量小碎步放大
            recent_volume = volume.iloc[-5:]  # 最近5天
            platform_volume = volume.iloc[-platform_period-10:-5]  # 平台期间
            pre_platform_volume = volume.iloc[-platform_period-20:-platform_period-10]  # 平台前

            # 平台期间成交量相对萎缩
            platform_avg_volume = platform_volume.mean()
            pre_platform_avg_volume = pre_platform_volume.mean()

            if pre_platform_avg_volume > 0:
                volume_shrink_ratio = platform_avg_volume / pre_platform_avg_volume
                if volume_shrink_ratio > 0.8:  # 成交量应该萎缩
                    return False

            # 最近成交量开始温和放大
            recent_avg_volume = recent_volume.mean()
            volume_expand_ratio = recent_avg_volume / platform_avg_volume if platform_avg_volume > 0 else 1

            # 成交量放大1.2-2倍（温和放大，不是暴涨）
            if not (1.2 <= volume_expand_ratio <= 2.5):
                return False

            # 条件4: 价格突破平台上沿
            current_price = close.iloc[-1]
            recent_high = high.iloc[-3:]  # 最近3天最高价

            # 突破平台上沿
            breakthrough = current_price > platform_high * 1.02  # 突破2%以上
            recent_strength = recent_high.max() > platform_high

            if not (breakthrough and recent_strength):
                return False

            # 条件5: 突破的有效性
            # 收盘价应该站稳在平台上沿之上
            close_above_platform = current_price > platform_high * 1.01

            # 最近几天没有大幅回落
            recent_low = low.iloc[-3:].min()
            no_major_pullback = recent_low > platform_high * 0.98

            if not (close_above_platform and no_major_pullback):
                return False

            # 条件6: 技术指标配合
            # MACD从底部向上
            macd_support = True
            try:
                dif, dea, hist = talib.MACD(close.values, fastperiod=12, slowperiod=26, signalperiod=9)
                if len(hist) > 5:
                    recent_hist = hist[-5:]
                    # MACD柱状线从负值向正值发展
                    hist_improving = recent_hist[-1] > recent_hist[-3]
                    macd_support = hist_improving
            except:
                pass

            # 条件7: 相对强度
            # 相对于大盘的表现
            relative_strength = True
            try:
                if len(close) > 10:
                    stock_return = (current_price - close.iloc[-11]) / close.iloc[-11]
                    # 这里简化处理，实际应该对比大盘指数
                    # 要求股票表现不能太差
                    relative_strength = stock_return > -0.1
            except:
                pass

            return macd_support and relative_strength

        except Exception as e:
            logger.error(f"计算脱离底部平台失败: {e}")
            return False

    def _cal_big_divergence(self, df):
        """
        大分歧策略：两天长上下影，一天上影线一天下影线

        特征：
        1. 连续两天出现长影线
        2. 一天长上影线，一天长下影线
        3. 市场分歧明显
        4. 可能是转折点信号

        :param df: 股票数据DataFrame
        :return: 是否为大分歧信号
        """
        try:
            if len(df) < 5:
                return False

            # 获取最近两天的数据
            day1 = df.iloc[-1]  # 最新一天
            day2 = df.iloc[-2]  # 前一天
            day3 = df.iloc[-3]  # 前两天（用于对比）

            # 计算影线长度
            def calc_shadow_ratios(day_data):
                """计算上下影线比例"""
                total_range = day_data.high - day_data.low
                if total_range <= 0:
                    return 0, 0, 0

                body_size = abs(day_data.close - day_data.open)
                upper_shadow = day_data.high - max(day_data.open, day_data.close)
                lower_shadow = min(day_data.open, day_data.close) - day_data.low

                body_ratio = body_size / total_range
                upper_shadow_ratio = upper_shadow / total_range
                lower_shadow_ratio = lower_shadow / total_range

                return body_ratio, upper_shadow_ratio, lower_shadow_ratio

            # 计算两天的影线比例
            body1, upper1, lower1 = calc_shadow_ratios(day1)
            body2, upper2, lower2 = calc_shadow_ratios(day2)

            # 条件1: 两天都有明显的长影线
            # 影线长度至少占总振幅的30%
            day1_has_long_shadow = max(upper1, lower1) >= 0.3
            day2_has_long_shadow = max(upper2, lower2) >= 0.3

            if not (day1_has_long_shadow and day2_has_long_shadow):
                return False

            # 条件2: 分歧模式识别
            # 模式1: 先长上影线，后长下影线（高位分歧）
            pattern1 = (upper2 >= 0.3 and upper2 > lower2 * 1.5 and  # 前一天长上影
                       lower1 >= 0.3 and lower1 > upper1 * 1.5)      # 当天长下影

            # 模式2: 先长下影线，后长上影线（低位分歧）
            pattern2 = (lower2 >= 0.3 and lower2 > upper2 * 1.5 and  # 前一天长下影
                       upper1 >= 0.3 and upper1 > lower1 * 1.5)      # 当天长上影

            # 模式3: 两天都是十字星形态（极度分歧）
            pattern3 = (body1 < 0.2 and body2 < 0.2 and  # 两天实体都很小
                       max(upper1, lower1) >= 0.35 and max(upper2, lower2) >= 0.35)

            has_divergence_pattern = pattern1 or pattern2 or pattern3

            if not has_divergence_pattern:
                return False

            # 条件3: 成交量配合
            # 分歧期间成交量应该放大
            volume_amplified = True
            try:
                recent_volume = df['volume'].iloc[-2:].mean()  # 两天平均成交量
                prev_volume = df['volume'].iloc[-7:-2].mean()  # 前5天平均成交量

                if prev_volume > 0:
                    volume_ratio = recent_volume / prev_volume
                    volume_amplified = volume_ratio >= 1.3  # 成交量放大30%以上
            except:
                pass

            # 条件4: 价格位置判断
            # 判断是在相对高位还是低位发生分歧
            position_reasonable = True
            try:
                current_price = day1.close

                # 计算20日内的价格位置
                if len(df) >= 20:
                    recent_20_high = df['high'].iloc[-20:].max()
                    recent_20_low = df['low'].iloc[-20:].min()
                    price_position = (current_price - recent_20_low) / (recent_20_high - recent_20_low)

                    # 在相对高位(>0.7)或低位(<0.3)的分歧更有意义
                    position_reasonable = price_position > 0.7 or price_position < 0.3
            except:
                pass

            # 条件5: 避免连续分歧
            # 前面不能有太多类似的分歧形态
            no_recent_divergence = True
            try:
                if len(df) >= 7:
                    # 检查前5天是否有类似的长影线
                    for i in range(3, 7):
                        if i < len(df):
                            prev_day = df.iloc[-i]
                            _, prev_upper, prev_lower = calc_shadow_ratios(prev_day)
                            if max(prev_upper, prev_lower) >= 0.3:
                                no_recent_divergence = False
                                break
            except:
                pass

            # 条件6: 技术指标确认
            # RSI在超买或超卖区域更容易形成转折
            rsi_confirmation = True
            try:
                close = df['close'].astype(float)
                rsi = talib.RSI(close.values, timeperiod=14)
                if len(rsi) > 0 and not pd.isna(rsi[-1]):
                    current_rsi = rsi[-1]
                    # RSI在30以下或70以上时分歧更有效
                    rsi_confirmation = current_rsi < 35 or current_rsi > 65
            except:
                pass

            return (volume_amplified and position_reasonable and
                   no_recent_divergence and rsi_confirmation)

        except Exception as e:
            logger.error(f"计算大分歧失败: {e}")
            return False

    def _cal_fund_accumulation(self, df):
        """
        资金吸筹策略：捕捉主力资金持续吸筹行为

        特征：
        1. 成交量温和放大，呈现阶梯式增长
        2. 价格在相对低位震荡，但重心逐步抬升
        3. 换手率适中，避免过度投机
        4. 资金流向呈现净流入状态
        5. 技术指标显示底部特征
        6. 大单净买入比例较高

        :param df: 股票数据DataFrame
        :return: 是否符合资金吸筹特征
        """
        try:
            if len(df) < 30:
                return False

            # 获取基础数据
            close = df['close'].astype(float)
            volume = df['volume'].astype(float)
            high = df['high'].astype(float)
            low = df['low'].astype(float)
            amount = df.get('amount', volume * close).astype(float)

            current_price = close.iloc[-1]

            # 条件1: 成交量温和放大特征
            def check_volume_accumulation():
                try:
                    # 分三个时间段检查成交量变化
                    recent_volume = volume.iloc[-10:].mean()  # 最近10天
                    mid_volume = volume.iloc[-20:-10].mean()  # 中期10天
                    early_volume = volume.iloc[-30:-20].mean()  # 早期10天

                    # 成交量应该呈现温和递增（降低要求）
                    volume_trend = (recent_volume > mid_volume * 1.05 and
                                  mid_volume > early_volume * 1.02)

                    # 避免暴量（成交量不能过度放大）
                    max_recent_volume = volume.iloc[-10:].max()
                    avg_volume = volume.iloc[-30:].mean()
                    no_excessive_volume = max_recent_volume < avg_volume * 4  # 放宽限制

                    return volume_trend and no_excessive_volume
                except:
                    return False

            # 条件2: 价格重心抬升
            def check_price_accumulation():
                try:
                    # 计算三个阶段的价格重心
                    recent_center = close.iloc[-10:].mean()
                    mid_center = close.iloc[-20:-10].mean()
                    early_center = close.iloc[-30:-20].mean()

                    # 价格重心逐步抬升（降低要求）
                    center_rising = (recent_center > mid_center * 1.01 and
                                   mid_center > early_center * 1.005)

                    # 当前价格不能偏离重心太远（放宽限制）
                    price_reasonable = abs(current_price - recent_center) / recent_center < 0.15

                    return center_rising and price_reasonable
                except:
                    return False

            # 条件3: 换手率适中
            def check_turnover_moderate():
                try:
                    # 估算换手率（简化计算）
                    recent_amount = amount.iloc[-10:].mean()
                    # 假设流通市值，实际应该从数据库获取
                    estimated_market_cap = current_price * volume.iloc[-1] * 100  # 简化估算

                    if estimated_market_cap > 0:
                        turnover_rate = recent_amount / estimated_market_cap
                        # 换手率在2%-8%之间比较合理
                        return 0.02 <= turnover_rate <= 0.08
                    return True  # 数据不足时不作为否决条件
                except:
                    return True

            # 条件4: 技术指标底部特征
            def check_technical_bottom():
                try:
                    # RSI从低位回升
                    rsi = talib.RSI(close.values, timeperiod=14)
                    if len(rsi) > 5:
                        current_rsi = rsi[-1]
                        prev_rsi = rsi[-5]
                        rsi_recovering = current_rsi > prev_rsi and 30 <= current_rsi <= 60
                    else:
                        rsi_recovering = True

                    # MACD底部特征
                    dif, dea, hist = talib.MACD(close.values, fastperiod=12, slowperiod=26, signalperiod=9)
                    if len(hist) > 5:
                        macd_improving = hist[-1] > hist[-3]  # MACD柱状线改善
                    else:
                        macd_improving = True

                    return rsi_recovering and macd_improving
                except:
                    return True

            # 条件5: 价格位置合理
            def check_price_position():
                try:
                    # 当前价格应该在近期低位附近
                    recent_high = high.iloc[-30:].max()
                    recent_low = low.iloc[-30:].min()

                    if recent_high > recent_low:
                        price_position = (current_price - recent_low) / (recent_high - recent_low)
                        # 价格在30%-70%区间，避免追高
                        return 0.3 <= price_position <= 0.7
                    return True
                except:
                    return True

            # 条件6: 成交金额稳定增长
            def check_amount_stability():
                try:
                    # 成交金额应该稳定增长，避免大起大落
                    recent_amounts = amount.iloc[-10:].values
                    amount_volatility = np.std(recent_amounts) / np.mean(recent_amounts)

                    # 波动率不能太高
                    return amount_volatility < 0.5
                except:
                    return True

            # 综合判断所有条件
            volume_ok = check_volume_accumulation()
            price_ok = check_price_accumulation()
            turnover_ok = check_turnover_moderate()
            technical_ok = check_technical_bottom()
            position_ok = check_price_position()
            amount_ok = check_amount_stability()

            # 至少满足6个条件中的3个（降低要求）
            conditions = [volume_ok, price_ok, turnover_ok, technical_ok, position_ok, amount_ok]
            satisfied_count = sum(conditions)

            return satisfied_count >= 3

        except Exception as e:
            logger.error(f"计算资金吸筹失败: {e}")
            return False
# 注意：旧的MockDataConfig类已被移除，现在使用统一的模拟数据管理系统
# 请使用 from config.mock_data_config import get_mock_data_manager

# TODO: 数据为假数据，需要从数据库或API获取
class FundamentalAnalyzer:
    """个股层次，基本面分析器"""

    def __init__(self):
        self.settings = get_settings()
        self.mock_manager = get_mock_data_manager()
    
    def analyze_fundamentals(self, code: str) -> Optional[FundamentalAnalysis]:
        """分析基本面"""
        try:
            logger.info(f"开始分析股票 {code} 基本面")
            
            # 获取基本面数据
            fundamental_data = self._get_fundamental_data(code)
            if not fundamental_data:
                logger.warning(f"股票 {code} 基本面数据不足")
                return None
            
            # 计算各维度评分
            valuation_score = self._calculate_valuation_score(fundamental_data)
            financial_score = self._calculate_financial_score(fundamental_data)
            growth_score = self._calculate_growth_score(fundamental_data)
            profitability_score = self._calculate_profitability_score(fundamental_data)
            
            # 计算综合评分
            overall_score = self._calculate_fundamental_overall_score(
                valuation_score, financial_score, growth_score, profitability_score
            )
            
            return FundamentalAnalysis(
                valuation_score=valuation_score,
                financial_score=financial_score,
                growth_score=growth_score,
                profitability_score=profitability_score,
                overall_score=overall_score,
                details=fundamental_data
            )
            
        except Exception as e:
            logger.error(f"分析基本面失败: {e}")
            return None
    
    # TODO: 从数据库或API获取基本面数据
    def _get_fundamental_data(self, code: str) -> Dict[str, Any]:
        """获取基本面数据"""
        try:
            # 获取股票源数据
            df_stock_source = df_source.loc[df_source['code'] == code]
            has_real_data = not df_stock_source.empty

            if not has_real_data:
                logger.warning(f"股票 {code} 没有源数据，使用模拟数据")
                # 使用模拟数据管理器获取数据
                mock_data = self.mock_manager.get_fundamental_data(code)
                # 补充一些额外的模拟字段
                seed = hash(code) % 10000
                mock_data.update({
                    'pe_ttm': 12.0 + (seed % 35),  # 12-47市盈率
                    'pb_mrq': 1.2 + (seed % 25) / 10,  # 1.2-3.7市净率
                    'gross_margin': 0.15 + (seed % 50) / 100,  # 15%-65%毛利率
                    'net_margin': 0.02 + (seed % 30) / 100,  # 2%-32%净利率
                    'debt_to_equity': 0.15 + (seed % 60) / 100,  # 15%-75%资产负债率
                    'revenue_growth': -0.2 + (seed % 50) / 100,  # -20%到30%营收增长率
                    'profit_growth': -0.3 + (seed % 70) / 100,  # -30%到40%净利润增长率
                    'market_cap': (30 + seed % 1500) * 100000000,  # 30亿到1530亿市值
                })

                self.mock_manager.log_mock_usage(code, "fundamental", list(mock_data.keys()))
                return mock_data

            # 有真实数据时，混合使用
            source = df_stock_source.iloc[0]
            mock_data = self.mock_manager.get_fundamental_data(code)

            # 优先使用真实数据，缺失时使用模拟数据
            result = {
                # 尝试从真实数据获取，失败则使用模拟数据
                'pe_ttm': self._get_real_or_mock(source, 'f9', mock_data.get('industry_avg_pe', 20.0)),
                'pb_mrq': self._get_real_or_mock(source, 'f23', mock_data.get('industry_avg_pb', 2.5)),
                'gross_margin': self._get_real_or_mock(source, 'f49', 0.3),
                'net_margin': self._get_real_or_mock(source, 'f129', 0.1),
                'debt_to_equity': self._get_real_or_mock(source, 'f57', 0.4),
                'revenue_growth': self._get_real_or_mock(source, 'f41', 0.1),
                'profit_growth': self._get_real_or_mock(source, 'f46', 0.1),
                'market_cap': self._get_real_or_mock(source, 'f20', 10000000000),

                # 使用模拟数据补充缺失字段
                'ps_ttm': mock_data['ps_ttm'],
                'pcf_ncf_ttm': mock_data['pcf_ncf_ttm'],
                'roe': mock_data['roe'],
                'roa': mock_data['roa'],
                'current_ratio': mock_data['current_ratio'],
                'quick_ratio': mock_data['quick_ratio'],
                'asset_growth': mock_data['asset_growth'],
                'equity_growth': mock_data['equity_growth'],
                'operating_cash_flow': mock_data['operating_cash_flow'],
                'free_cash_flow': mock_data['free_cash_flow'],
                'industry_avg_pe': mock_data['industry_avg_pe'],
                'industry_avg_pb': mock_data['industry_avg_pb'],
            }

            # 记录使用的模拟数据字段
            mock_fields = [k for k, v in result.items() if k in mock_data]
            if mock_fields:
                self.mock_manager.log_mock_usage(code, "fundamental_partial", mock_fields)

            return result

        except Exception as e:
            logger.error(f"获取基本面数据失败: {e}")
            return {}

    def _get_real_or_mock(self, source, field_name, default_value):
        """获取真实数据或默认值"""
        try:
            value = source.get(field_name)
            if value is not None and not pd.isna(value) and value != 0:
                return float(value)
            return default_value
        except:
            return default_value
    
    def _calculate_valuation_score(self, data: Dict[str, Any]) -> float:
        """计算估值评分"""
        try:
            score = 50.0  # 基础分
            
            # 市盈率评分
            pe_ttm = data.get('pe_ttm', 0)
            industry_avg_pe = data.get('industry_avg_pe', 20.0)
            
            if pe_ttm > 0:
                pe_ratio = pe_ttm / industry_avg_pe
                if pe_ratio < 0.5:
                    score += 20  # 严重低估
                elif pe_ratio < 0.8:
                    score += 15  # 低估
                elif pe_ratio < 1.2:
                    score += 10  # 合理
                elif pe_ratio < 1.5:
                    score += 5   # 略高
                else:
                    score -= 10  # 高估
            
            # 市净率评分
            pb_mrq = data.get('pb_mrq', 0)
            industry_avg_pb = data.get('industry_avg_pb', 2.5)
            
            if pb_mrq > 0:
                pb_ratio = pb_mrq / industry_avg_pb
                if pb_ratio < 0.6:
                    score += 15
                elif pb_ratio < 0.9:
                    score += 10
                elif pb_ratio < 1.3:
                    score += 5
                else:
                    score -= 5
            
            # 市销率评分
            ps_ttm = data.get('ps_ttm', 0)
            if ps_ttm > 0:
                if ps_ttm < 1:
                    score += 10
                elif ps_ttm < 3:
                    score += 5
                elif ps_ttm > 10:
                    score -= 10
            
            # 市现率评分
            pcf_ncf_ttm = data.get('pcf_ncf_ttm', 0)
            if pcf_ncf_ttm > 0:
                if pcf_ncf_ttm < 10:
                    score += 10
                elif pcf_ncf_ttm < 20:
                    score += 5
                elif pcf_ncf_ttm > 50:
                    score -= 10
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算估值评分失败: {e}")
            return 50.0
    
    def _calculate_financial_score(self, data: Dict[str, Any]) -> float:
        """计算财务评分"""
        try:
            score = 50.0  # 基础分
            
            # 资产负债率评分
            debt_to_equity = data.get('debt_to_equity', 0)
            if debt_to_equity < 0.3:
                score += 15  # 低负债
            elif debt_to_equity < 0.5:
                score += 10  # 合理负债
            elif debt_to_equity < 0.7:
                score += 5   # 较高负债
            else:
                score -= 15  # 高负债
            
            # 流动比率评分
            current_ratio = data.get('current_ratio', 0)
            if current_ratio > 2.5:
                score += 10
            elif current_ratio > 1.5:
                score += 5
            elif current_ratio < 1:
                score -= 10
            
            # 速动比率评分
            quick_ratio = data.get('quick_ratio', 0)
            if quick_ratio > 1.5:
                score += 10
            elif quick_ratio > 1:
                score += 5
            elif quick_ratio < 0.5:
                score -= 10
            
            # 现金流评分
            operating_cash_flow = data.get('operating_cash_flow', 0)
            free_cash_flow = data.get('free_cash_flow', 0)
            
            if operating_cash_flow > 0 and free_cash_flow > 0:
                score += 15
            elif operating_cash_flow > 0:
                score += 10
            else:
                score -= 15
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算财务评分失败: {e}")
            return 50.0
    
    def _calculate_growth_score(self, data: Dict[str, Any]) -> float:
        """计算成长性评分"""
        try:
            score = 50.0  # 基础分
            
            # 营收增长率评分
            revenue_growth = data.get('revenue_growth', 0)
            if revenue_growth > 0.5:
                score += 20  # 高速增长
            elif revenue_growth > 0.2:
                score += 15  # 较快增长
            elif revenue_growth > 0.1:
                score += 10  # 稳定增长
            elif revenue_growth > 0:
                score += 5   # 微增长
            else:
                score -= 15  # 负增长
            
            # 净利润增长率评分
            profit_growth = data.get('profit_growth', 0)
            if profit_growth > 0.5:
                score += 20
            elif profit_growth > 0.2:
                score += 15
            elif profit_growth > 0.1:
                score += 10
            elif profit_growth > 0:
                score += 5
            else:
                score -= 15
            
            # 资产增长率评分
            asset_growth = data.get('asset_growth', 0)
            if asset_growth > 0.2:
                score += 10
            elif asset_growth > 0.1:
                score += 5
            elif asset_growth < 0:
                score -= 5
            
            # 净资产增长率评分
            equity_growth = data.get('equity_growth', 0)
            if equity_growth > 0.2:
                score += 10
            elif equity_growth > 0.1:
                score += 5
            elif equity_growth < 0:
                score -= 5
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算成长性评分失败: {e}")
            return 50.0
    
    def _calculate_profitability_score(self, data: Dict[str, Any]) -> float:
        """计算盈利能力评分"""
        try:
            score = 50.0  # 基础分
            
            # ROE评分
            roe = data.get('roe', 0)
            if roe > 0.25:
                score += 20  # 优秀
            elif roe > 0.15:
                score += 15  # 良好
            elif roe > 0.10:
                score += 10  # 一般
            elif roe > 0.05:
                score += 5   # 较差
            else:
                score -= 15  # 很差
            
            # ROA评分
            roa = data.get('roa', 0)
            if roa > 0.15:
                score += 15
            elif roa > 0.10:
                score += 10
            elif roa > 0.05:
                score += 5
            elif roa < 0:
                score -= 10
            
            # 毛利率评分
            gross_margin = data.get('gross_margin', 0)
            if gross_margin > 0.5:
                score += 15
            elif gross_margin > 0.3:
                score += 10
            elif gross_margin > 0.2:
                score += 5
            elif gross_margin < 0.1:
                score -= 10
            
            # 净利率评分
            net_margin = data.get('net_margin', 0)
            if net_margin > 0.2:
                score += 15
            elif net_margin > 0.1:
                score += 10
            elif net_margin > 0.05:
                score += 5
            elif net_margin < 0:
                score -= 15
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算盈利能力评分失败: {e}")
            return 50.0
    
    def _calculate_fundamental_overall_score(self, valuation_score: float, 
                                           financial_score: float, 
                                           growth_score: float, 
                                           profitability_score: float) -> float:
        """计算基本面综合评分"""
        try:
            # 权重配置
            weights = {
                'valuation': 0.20,      # 估值权重25%
                'financial': 0.10,      # 财务权重25%
                'growth': 0.45,         # 成长性权重25%
                'profitability': 0.25   # 盈利能力权重25%
            }
            
            overall_score = (
                valuation_score * weights['valuation'] +
                financial_score * weights['financial'] +
                growth_score * weights['growth'] +
                profitability_score * weights['profitability']
            )
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"计算基本面综合评分失败: {e}")
            return 50.0

# TODO: 数据为假数据，需要从数据库或API获取
class SentimentAnalyzer:
    """风口分析器"""

    def __init__(self):
        self.settings = get_settings()
        self.mock_manager = get_mock_data_manager()
    
    def analyze_sentiment(self, code: str) -> Optional[SentimentAnalysis]:
        """分析风口"""
        try:
            logger.info(f"开始分析股票 {code} 风口面")
            
            # 获取风口数据
            sentiment_data = self._get_sentiment_data(code)
            if not sentiment_data:
                logger.warning(f"股票 {code} 风口数据不足")
                return None
            
            # 计算各维度评分
            industry_hot_score = self._calculate_industry_hot_score(sentiment_data)
            policy_support_score = self._calculate_policy_support_score(sentiment_data)


            # TODO: 暂时计算
            market_attention_score = 0
            try:
                if code in stock_trend_dict:
                    # 热度爬虫每小时爬一次，满分应该是2400分
                    hot_score = stock_trend_dict[code] / 10
                    if hot_score > 0:
                        market_attention_score = min(100, hot_score)
            except Exception as e:
                logger.error(f"计算市场关注度评分失败: {e}")
                market_attention_score = 0

            # market_attention_score = self._calculate_market_attention_score(sentiment_data)
            news_sentiment_score = self._calculate_news_sentiment_score(sentiment_data)
            
            # 计算综合评分
            overall_score = self._calculate_sentiment_overall_score(
                industry_hot_score, policy_support_score, 
                market_attention_score, news_sentiment_score
            )
            
            return SentimentAnalysis(
                industry_hot_score=industry_hot_score,
                policy_support_score=policy_support_score,
                market_attention_score=market_attention_score,
                news_sentiment_score=news_sentiment_score,
                overall_score=overall_score,
                details=sentiment_data
            )
            
        except Exception as e:
            logger.error(f"分析风口失败: {e}")
            return None
    
    def _get_sentiment_data(self, code: str) -> Dict[str, Any]:
        """获取风口数据"""
        try:
            # 这里应该从数据库或API获取风口数据
            # 使用新的模拟数据管理器
            mock_data = self.mock_manager.get_sentiment_data(code)

            if not mock_data:
                logger.warning(f"股票 {code} 无法获取风口面数据")
                return {}

            # 记录模拟数据使用情况
            self.mock_manager.log_mock_usage(code, "sentiment", list(mock_data.keys()))

            logger.debug(f"股票 {code} 风口面数据: 行业排名={mock_data['industry_rank']}, "
                        f"政策支持={mock_data['policy_support_level']}, "
                        f"市场关注度排名={mock_data['market_attention_rank']}")

            return mock_data

        except Exception as e:
            logger.error(f"获取风口数据失败: {e}")
            return {}
    
    def _calculate_industry_hot_score(self, data: Dict[str, Any]) -> float:
        """计算行业热度评分"""
        try:
            score = 50.0  # 基础分
            
            # 行业排名评分
            industry_rank = data.get('industry_rank', 100)
            if industry_rank <= 5:
                score += 25  # 行业龙头
            elif industry_rank <= 10:
                score += 20  # 行业前10
            elif industry_rank <= 20:
                score += 15  # 行业前20
            elif industry_rank <= 50:
                score += 10  # 行业前50
            else:
                score -= 10  # 行业排名靠后
            
            # 行业增长率评分
            industry_growth = data.get('industry_growth', 0)
            if industry_growth > 0.3:
                score += 20  # 高速增长行业
            elif industry_growth > 0.2:
                score += 15  # 较快增长行业
            elif industry_growth > 0.1:
                score += 10  # 稳定增长行业
            elif industry_growth > 0:
                score += 5   # 微增长行业
            else:
                score -= 15  # 负增长行业
            
            # 行业竞争度评分
            industry_competition = data.get('industry_competition', 0.5)
            if industry_competition < 0.3:
                score += 15  # 低竞争
            elif industry_competition < 0.6:
                score += 10  # 中等竞争
            elif industry_competition < 0.8:
                score += 5   # 高竞争
            else:
                score -= 10  # 极高竞争
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算行业热度评分失败: {e}")
            return 50.0
    
    def _calculate_policy_support_score(self, data: Dict[str, Any]) -> float:
        """计算政策支持评分"""
        try:
            score = 50.0  # 基础分
            
            # 政策支持等级评分
            policy_support_level = data.get('policy_support_level', 3)
            if policy_support_level >= 5:
                score += 25  # 强政策支持
            elif policy_support_level >= 4:
                score += 20  # 较强政策支持
            elif policy_support_level >= 3:
                score += 10  # 一般政策支持
            elif policy_support_level >= 2:
                score += 5   # 较弱政策支持
            else:
                score -= 15  # 无政策支持
            
            # 政策相关新闻数评分
            policy_news_count = data.get('policy_news_count', 0)
            if policy_news_count > 20:
                score += 15
            elif policy_news_count > 10:
                score += 10
            elif policy_news_count > 5:
                score += 5
            else:
                score -= 5
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算政策支持评分失败: {e}")
            return 50.0
    
    def _calculate_market_attention_score(self, data: Dict[str, Any]) -> float:
        """计算市场关注度评分"""
        try:
            score = 50.0  # 基础分
            
            # 市场关注度排名评分
            market_attention_rank = data.get('market_attention_rank', 100)
            if market_attention_rank <= 10:
                score += 20  # 高度关注
            elif market_attention_rank <= 30:
                score += 15  # 较高关注
            elif market_attention_rank <= 50:
                score += 10  # 中等关注
            elif market_attention_rank <= 100:
                score += 5   # 一般关注
            else:
                score -= 10  # 低关注
            
            # 搜索量评分
            search_volume = data.get('search_volume', 0)
            if search_volume > 100000:
                score += 15
            elif search_volume > 50000:
                score += 10
            elif search_volume > 10000:
                score += 5
            else:
                score -= 5
            
            # 新闻数量评分
            news_count = data.get('news_count', 0)
            if news_count > 100:
                score += 10
            elif news_count > 50:
                score += 5
            elif news_count < 10:
                score -= 5
            
            # 社交媒体提及数评分
            social_media_mentions = data.get('social_media_mentions', 0)
            if social_media_mentions > 5000:
                score += 10
            elif social_media_mentions > 1000:
                score += 5
            elif social_media_mentions < 100:
                score -= 5
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算市场关注度评分失败: {e}")
            return 50.0
    
    def _calculate_news_sentiment_score(self, data: Dict[str, Any]) -> float:
        """计算新闻情感评分"""
        try:
            score = 50.0  # 基础分
            
            # 正面新闻比例评分
            positive_news_ratio = data.get('positive_news_ratio', 0.5)
            if positive_news_ratio > 0.7:
                score += 20  # 非常正面
            elif positive_news_ratio > 0.6:
                score += 15  # 较正面
            elif positive_news_ratio > 0.5:
                score += 10  # 略正面
            elif positive_news_ratio > 0.4:
                score += 5   # 中性偏正面
            else:
                score -= 10  # 负面
            
            # 负面新闻比例评分
            negative_news_ratio = data.get('negative_news_ratio', 0.2)
            if negative_news_ratio > 0.4:
                score -= 20  # 非常负面
            elif negative_news_ratio > 0.3:
                score -= 15  # 较负面
            elif negative_news_ratio > 0.2:
                score -= 10  # 略负面
            elif negative_news_ratio > 0.1:
                score -= 5   # 中性偏负面
            
            # 分析师评级评分
            analyst_rating = data.get('analyst_rating', 3.0)
            if analyst_rating >= 4.5:
                score += 15
            elif analyst_rating >= 4.0:
                score += 10
            elif analyst_rating >= 3.5:
                score += 5
            elif analyst_rating < 2.5:
                score -= 10
            
            # 机构持股比例评分
            institutional_holdings = data.get('institutional_holdings', 0.3)
            if institutional_holdings > 0.6:
                score += 10  # 机构高度认可
            elif institutional_holdings > 0.4:
                score += 5   # 机构认可
            elif institutional_holdings < 0.2:
                score -= 5   # 机构认可度低
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算新闻情感评分失败: {e}")
            return 50.0
    
    def _calculate_sentiment_overall_score(self, industry_hot_score: float,
                                         policy_support_score: float,
                                         market_attention_score: float,
                                         news_sentiment_score: float) -> float:
        """计算风口综合评分"""
        try:
            # 权重配置
            weights = {
                'industry_hot': 0.20,        # 行业热度权重25%
                'policy_support': 0.15,      # 政策支持权重25%
                'market_attention': 0.45,    # 市场关注度权重25%
                'news_sentiment': 0.20       # 新闻情感权重25%
            }
            
            overall_score = (
                industry_hot_score * weights['industry_hot'] +
                policy_support_score * weights['policy_support'] +
                market_attention_score * weights['market_attention'] +
                news_sentiment_score * weights['news_sentiment']
            )
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"计算风口综合评分失败: {e}")
            return 50.0

class LiquidityAnalyzer:
    """资金面分析器"""

    def __init__(self):
        self.settings = get_settings()
        self.mock_manager = get_mock_data_manager()
    
    def analyze_liquidity(self, code: str) -> Optional[LiquidityAnalysis]:
        """分析资金面"""
        try:
            logger.info(f"开始分析股票 {code} 资金面")
            
            # 获取资金面数据
            liquidity_data = self._get_liquidity_data(code)
            if not liquidity_data:
                logger.warning(f"股票 {code} 资金面数据不足")
                return None
            
            # 计算各维度评分
            turnover_score = self._calculate_turnover_score(liquidity_data)
            volume_score = self._calculate_volume_score(liquidity_data)
            north_money_score = self._calculate_north_money_score(liquidity_data)
            margin_score = self._calculate_margin_score(liquidity_data)
            institutional_score = self._calculate_institutional_score(liquidity_data)
            
            # 计算综合评分
            overall_score = self._calculate_liquidity_overall_score(
                turnover_score, volume_score, north_money_score, 
                margin_score, institutional_score
            )
            
            return LiquidityAnalysis(
                turnover_score=turnover_score,
                volume_score=volume_score,
                north_money_score=north_money_score,
                margin_score=margin_score,
                institutional_score=institutional_score,
                overall_score=overall_score,
                details=liquidity_data
            )
            
        except Exception as e:
            logger.error(f"分析资金面失败: {e}")
            return None
    
    def _get_liquidity_data(self, code: str) -> Dict[str, Any]:
        """
        获取资金面数据
        优先使用真实数据，缺失时使用模拟数据
        """
        try:
            # 获取股票源数据
            df_stock_source = df_source.loc[df_source['code'] == code]
            has_real_data = not df_stock_source.empty

            if not has_real_data:
                logger.warning(f"股票 {code} 没有源数据，使用模拟数据")
                # 使用模拟数据管理器获取数据
                mock_data = self.mock_manager.get_liquidity_data(code)
                self.mock_manager.log_mock_usage(code, "liquidity", list(mock_data.keys()))
                return mock_data

            # 有真实数据时，混合使用
            source = df_stock_source.iloc[0]
            mock_data = self.mock_manager.get_liquidity_data(code)

            # 优先使用真实数据，缺失时使用模拟数据
            result = {
                # 尝试从真实数据获取
                'turnover_rate': self._get_real_or_mock_value(source, 'f8', mock_data.get('turnover_rate', 2.0)),
                'volume_ratio': self._get_real_or_mock_value(source, 'f5', mock_data.get('volume_ratio', 1.0)),
                'amount': self._get_real_or_mock_value(source, 'f6', mock_data.get('amount', 100000000)),
                'market_cap': self._get_real_or_mock_value(source, 'f20', mock_data.get('market_cap', 10000000000)),
                'float_market_cap': self._get_real_or_mock_value(source, 'f21', mock_data.get('float_market_cap', 8000000000)),

                # 使用模拟数据补充缺失字段
                'avg_turnover_rate': mock_data.get('avg_turnover_rate', 2.0),
                'avg_amount': mock_data.get('avg_amount', 900000000),
                'north_money_inflow': mock_data.get('north_money_inflow', 35000000),
                'north_money_ratio': mock_data.get('north_money_ratio', 0.18),
                'margin_balance': mock_data.get('margin_balance', 600000000),
                'margin_ratio': mock_data.get('margin_ratio', 0.08),
                'institutional_holdings': mock_data.get('institutional_holdings', 0.42),
                'institutional_net_buy': mock_data.get('institutional_net_buy', 12000000),
                'retail_holdings': mock_data.get('retail_holdings', 0.38),
                'foreign_holdings': mock_data.get('foreign_holdings', 0.12),
                'liquidity_ratio': mock_data.get('liquidity_ratio', 0.87),
            }

            # 记录使用的模拟数据字段
            mock_fields = [k for k, v in result.items() if k in mock_data and v == mock_data[k]]
            if mock_fields:
                self.mock_manager.log_mock_usage(code, "liquidity_partial", mock_fields)

            return result

        except Exception as e:
            logger.error(f"获取资金面数据失败: {e}")
            return {}

    def _get_real_or_mock_value(self, source, field_name, default_value):
        """获取真实数据或默认值"""
        try:
            value = source.get(field_name)
            if value is not None and not pd.isna(value) and value != 0:
                return float(value)
            return default_value
        except:
            return default_value
    
    def _calculate_turnover_score(self, data: Dict[str, Any]) -> float:
        """计算换手率评分"""
        try:
            score = 50.0  # 基础分
            
            turnover_rate = data.get('turnover_rate', 0)
            avg_turnover_rate = data.get('avg_turnover_rate', 2.0)
            
            if turnover_rate > 0:
                turnover_ratio = turnover_rate / avg_turnover_rate
                if turnover_ratio > 2.0:
                    score += 20  # 高换手率
                elif turnover_ratio > 1.5:
                    score += 15  # 较高换手率
                elif turnover_ratio > 1.2:
                    score += 10  # 中等换手率
                elif turnover_ratio > 0.8:
                    score += 5   # 正常换手率
                else:
                    score -= 10  # 低换手率
            
            # 换手率绝对值评分
            if turnover_rate > 5:
                score += 10  # 非常活跃
            elif turnover_rate > 3:
                score += 5   # 活跃
            elif turnover_rate < 1:
                score -= 15  # 不活跃
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算换手率评分失败: {e}")
            return 50.0
    
    def _calculate_volume_score(self, data: Dict[str, Any]) -> float:
        """计算成交量评分"""
        try:
            score = 50.0  # 基础分
            
            volume_ratio = data.get('volume_ratio', 1.0)
            amount = data.get('amount', 0)
            avg_amount = data.get('avg_amount', 1000000000)
            
            # 成交量比率评分
            if volume_ratio > 2.0:
                score += 20  # 放量
            elif volume_ratio > 1.5:
                score += 15  # 量增
            elif volume_ratio > 1.2:
                score += 10  # 量升
            elif volume_ratio > 0.8:
                score += 5   # 正常
            else:
                score -= 10  # 缩量
            
            # 成交额评分
            if amount > avg_amount * 2:
                score += 15
            elif amount > avg_amount * 1.5:
                score += 10
            elif amount < avg_amount * 0.5:
                score -= 15
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算成交量评分失败: {e}")
            return 50.0
    
    def _calculate_north_money_score(self, data: Dict[str, Any]) -> float:
        """计算北向资金评分"""
        try:
            score = 50.0  # 基础分
            
            north_money_inflow = data.get('north_money_inflow', 0)
            north_money_ratio = data.get('north_money_ratio', 0)
            
            # 北向资金流入评分
            if north_money_inflow > 100000000:  # 1亿以上
                score += 20
            elif north_money_inflow > 50000000:  # 5000万以上
                score += 15
            elif north_money_inflow > 10000000:  # 1000万以上
                score += 10
            elif north_money_inflow > 0:
                score += 5
            elif north_money_inflow < -50000000:  # 流出5000万以上
                score -= 20
            elif north_money_inflow < -10000000:  # 流出1000万以上
                score -= 15
            elif north_money_inflow < 0:
                score -= 10
            
            # 北向资金占比评分
            if north_money_ratio > 0.3:
                score += 10  # 外资高度认可
            elif north_money_ratio > 0.2:
                score += 5   # 外资认可
            elif north_money_ratio < 0.05:
                score -= 5   # 外资认可度低
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算北向资金评分失败: {e}")
            return 50.0
    
    def _calculate_margin_score(self, data: Dict[str, Any]) -> float:
        """计算融资融券评分"""
        try:
            score = 50.0  # 基础分
            
            margin_balance = data.get('margin_balance', 0)
            margin_ratio = data.get('margin_ratio', 0)
            
            # 融资余额评分
            if margin_balance > 1000000000:  # 10亿以上
                score += 15
            elif margin_balance > 500000000:  # 5亿以上
                score += 10
            elif margin_balance > 100000000:  # 1亿以上
                score += 5
            elif margin_balance < 10000000:  # 1000万以下
                score -= 10
            
            # 融资比例评分
            if margin_ratio > 0.15:
                score -= 15  # 融资比例过高
            elif margin_ratio > 0.10:
                score -= 10  # 融资比例较高
            elif margin_ratio > 0.05:
                score += 5   # 融资比例适中
            elif margin_ratio < 0.01:
                score -= 5   # 融资比例过低
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算融资融券评分失败: {e}")
            return 50.0
    
    def _calculate_institutional_score(self, data: Dict[str, Any]) -> float:
        """计算机构资金评分"""
        try:
            score = 50.0  # 基础分
            
            institutional_holdings = data.get('institutional_holdings', 0)
            institutional_net_buy = data.get('institutional_net_buy', 0)
            foreign_holdings = data.get('foreign_holdings', 0)
            
            # 机构持股比例评分
            if institutional_holdings > 0.7:
                score += 15  # 机构高度认可
            elif institutional_holdings > 0.5:
                score += 10  # 机构认可
            elif institutional_holdings > 0.3:
                score += 5   # 机构一般认可
            elif institutional_holdings < 0.1:
                score -= 10  # 机构认可度低
            
            # 机构净买入评分
            if institutional_net_buy > 50000000:  # 5000万以上
                score += 15
            elif institutional_net_buy > 20000000:  # 2000万以上
                score += 10
            elif institutional_net_buy > 5000000:  # 500万以上
                score += 5
            elif institutional_net_buy < -20000000:  # 净卖出2000万以上
                score -= 15
            elif institutional_net_buy < -5000000:  # 净卖出500万以上
                score -= 10
            elif institutional_net_buy < 0:
                score -= 5
            
            # 外资持股评分
            if foreign_holdings > 0.3:
                score += 10  # 外资高度认可
            elif foreign_holdings > 0.2:
                score += 5   # 外资认可
            elif foreign_holdings < 0.05:
                score -= 5   # 外资认可度低
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算机构资金评分失败: {e}")
            return 50.0
    
    def _calculate_liquidity_overall_score(self, turnover_score: float,
                                         volume_score: float,
                                         north_money_score: float,
                                         margin_score: float,
                                         institutional_score: float) -> float:
        """计算资金面综合评分"""
        try:
            # 权重配置
            weights = {
                'turnover': 0.35,        # 换手率权重25%
                'volume': 0.35,          # 成交量权重25%
                'north_money': 0.10,     # 北向资金权重20%
                'margin': 0.10,          # 融资融券权重15%
                'institutional': 0.10    # 机构资金权重15%
            }
            
            overall_score = (
                turnover_score * weights['turnover'] +
                volume_score * weights['volume'] +
                north_money_score * weights['north_money'] +
                margin_score * weights['margin'] +
                institutional_score * weights['institutional']
            )
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"计算资金面综合评分失败: {e}")
            return 50.0


class RiskAnalyzer:
    """
    风险面分析器（Risk Dimension Analyzer）
    提供风险面各项子指标的数据获取与评分方法。
    """
    def __init__(self):
        self.settings = get_settings()
        self.mock_manager = get_mock_data_manager()

    def analyze_risk(self, code: str = None) -> Optional[RiskAnalysis]:
        """
        分析风险面，返回RiskAnalysis对象
        code参数可选，若为None则分析全市场风险
        """
        try:
            # 获取风险面数据（模拟数据）
            risk_data = self._get_risk_data(code)
            if not risk_data:
                logger.warning(f"{code or '全市场'} 风险面数据不足")
                return None

            # 计算各子项评分
            volatility_score = self._calculate_volatility_score(risk_data)
            liquidity_risk_score = self._calculate_liquidity_risk_score(risk_data)
            systematic_risk_score = self._calculate_systematic_risk_score(risk_data)
            concentration_risk_score = self._calculate_concentration_risk_score(risk_data)

            # 计算综合评分
            overall_score = self._calculate_risk_overall_score(
                volatility_score, liquidity_risk_score, systematic_risk_score, concentration_risk_score
            )

            return RiskAnalysis(
                volatility_score=volatility_score,
                liquidity_risk_score=liquidity_risk_score,
                systematic_risk_score=systematic_risk_score,
                concentration_risk_score=concentration_risk_score,
                overall_score=overall_score,
                details=risk_data
            )
        except Exception as e:
            logger.error(f"分析风险面失败: {e}")
            return None

    def _get_risk_data(self, code: str = None) -> Dict[str, Any]:
        """
        获取风险面数据
        使用模拟数据管理器获取一致性数据
        """
        try:
            if code is None:
                # 全市场风险分析，使用固定种子
                code = "MARKET_RISK"

            # 使用模拟数据管理器获取数据
            mock_data = self.mock_manager.get_risk_data(code)

            if not mock_data:
                logger.warning(f"{code or '全市场'} 无法获取风险面数据")
                return {}

            # 记录模拟数据使用情况
            self.mock_manager.log_mock_usage(code, "risk", list(mock_data.keys()))

            logger.debug(f"{code or '全市场'} 风险面数据: 波动率={mock_data['volatility']:.3f}, "
                        f"Beta={mock_data['beta']:.2f}, 风险评分={mock_data['risk_score']:.1f}")

            return mock_data

        except Exception as e:
            logger.error(f"获取风险面数据失败: {e}")
            return {}

    def _calculate_volatility_score(self, data: Dict[str, Any]) -> float:
        """计算波动性风险评分"""
        try:
            volatility = data.get('volatility', 0.3)
            score = 50.0
            if volatility > 0.5:
                score -= 20
            elif volatility > 0.3:
                score -= 10
            elif volatility < 0.15:
                score += 10
            return max(0, min(100, score))
        except Exception as e:
            logger.error(f"计算波动性风险评分失败: {e}")
            return 50.0

    def _calculate_liquidity_risk_score(self, data: Dict[str, Any]) -> float:
        """计算流动性风险评分"""
        try:
            illiquidity = data.get('illiquidity', 0.1)
            score = 50.0
            if illiquidity > 0.2:
                score -= 20
            elif illiquidity > 0.1:
                score -= 10
            elif illiquidity < 0.05:
                score += 10
            return max(0, min(100, score))
        except Exception as e:
            logger.error(f"计算流动性风险评分失败: {e}")
            return 50.0

    def _calculate_systematic_risk_score(self, data: Dict[str, Any]) -> float:
        """计算系统性风险评分"""
        try:
            beta = data.get('beta', 1.0)
            score = 50.0
            if beta > 1.5:
                score -= 20
            elif beta > 1.2:
                score -= 10
            elif beta < 0.8:
                score += 10
            return max(0, min(100, score))
        except Exception as e:
            logger.error(f"计算系统性风险评分失败: {e}")
            return 50.0

    def _calculate_concentration_risk_score(self, data: Dict[str, Any]) -> float:
        """计算集中度风险评分"""
        try:
            concentration = data.get('concentration', 0.2)
            score = 50.0
            if concentration > 0.4:
                score -= 20
            elif concentration > 0.3:
                score -= 10
            elif concentration < 0.1:
                score += 10
            return max(0, min(100, score))
        except Exception as e:
            logger.error(f"计算集中度风险评分失败: {e}")
            return 50.0

    def _calculate_risk_overall_score(self, volatility_score: float, liquidity_risk_score: float, systematic_risk_score: float, concentration_risk_score: float) -> float:
        """计算风险面综合评分"""
        try:
            weights = {
                'volatility': 0.30,        # 波动性权重30%
                'liquidity': 0.25,         # 流动性风险权重25%
                'systematic': 0.25,        # 系统性风险权重25%
                'concentration': 0.20      # 集中度风险权重20%
            }
            overall_score = (
                volatility_score * weights['volatility'] +
                liquidity_risk_score * weights['liquidity'] +
                systematic_risk_score * weights['systematic'] +
                concentration_risk_score * weights['concentration']
            )
            return round(overall_score, 2)
        except Exception as e:
            logger.error(f"计算风险面综合评分失败: {e}")
            return 50.0

class MarketAnalyzer:
    """市场面分析器"""

    def __init__(self):
        self.settings = get_settings()
        self.mock_manager = get_mock_data_manager()
    
    def analyze_market(self, code: str) -> Optional[MarketAnalysis]:
        """分析市场面"""
        try:
            logger.info(f"开始分析股票 {code} 市场面")
            
            # 获取市场面数据
            market_data = self._get_market_data(code)
            if not market_data:
                logger.warning(f"股票 {code} 市场面数据不足")
                return None
            
            # 计算各维度评分
            market_sentiment_score = self._calculate_market_sentiment_score(market_data)
            sector_rotation_score = self._calculate_sector_rotation_score(market_data)
            market_structure_score = self._calculate_market_structure_score(market_data)
            market_breadth_score = self._calculate_market_breadth_score(market_data)
            
            # 计算综合评分
            overall_score = self._calculate_market_overall_score(
                market_sentiment_score, sector_rotation_score,
                market_structure_score, market_breadth_score
            )
            
            return MarketAnalysis(
                market_sentiment_score=market_sentiment_score,
                sector_rotation_score=sector_rotation_score,
                market_structure_score=market_structure_score,
                market_breadth_score=market_breadth_score,
                overall_score=overall_score,
                details=market_data
            )
            
        except Exception as e:
            logger.error(f"分析市场面失败: {e}")
            return None
    
    def _get_market_data(self, code: str) -> Dict[str, Any]:
        """
        获取市场面数据
        使用模拟数据管理器获取一致性数据
        """
        try:
            # 使用模拟数据管理器获取数据
            mock_data = self.mock_manager.get_market_data(code)

            if not mock_data:
                logger.warning(f"股票 {code} 无法获取市场面数据")
                return {}

            # 记录模拟数据使用情况
            self.mock_manager.log_mock_usage(code, "market", list(mock_data.keys()))

            logger.debug(f"股票 {code} 市场面数据: 行业={mock_data['industry']}, "
                        f"板块={mock_data['sector']}, 市场情绪={mock_data['market_sentiment']:.2f}")

            return mock_data

        except Exception as e:
            logger.error(f"获取市场面数据失败: {e}")
            return {}
    
    def _calculate_market_sentiment_score(self, data: Dict[str, Any]) -> float:
        """计算市场情绪评分"""
        try:
            score = 50.0  # 基础分
            
            market_sentiment = data.get('market_sentiment', 0.5)
            advance_decline_ratio = data.get('advance_decline_ratio', 1.0)
            limit_up_count = data.get('limit_up_count', 0)
            limit_down_count = data.get('limit_down_count', 0)
            
            # 市场情绪指数评分
            if market_sentiment > 0.8:
                score += 20  # 极度乐观
            elif market_sentiment > 0.7:
                score += 15  # 乐观
            elif market_sentiment > 0.6:
                score += 10  # 偏乐观
            elif market_sentiment > 0.4:
                score += 5   # 中性
            elif market_sentiment > 0.3:
                score -= 10  # 悲观
            else:
                score -= 20  # 极度悲观
            
            # 涨跌比评分
            if advance_decline_ratio > 2.0:
                score += 15
            elif advance_decline_ratio > 1.5:
                score += 10
            elif advance_decline_ratio > 1.2:
                score += 5
            elif advance_decline_ratio < 0.5:
                score -= 15
            elif advance_decline_ratio < 0.8:
                score -= 10
            
            # 涨跌停数量评分
            if limit_up_count > 100:
                score += 10
            elif limit_up_count > 50:
                score += 5
            elif limit_down_count > 50:
                score -= 15
            elif limit_down_count > 20:
                score -= 10
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算市场情绪评分失败: {e}")
            return 50.0
    
    def _calculate_sector_rotation_score(self, data: Dict[str, Any]) -> float:
        """计算板块轮动评分"""
        try:
            score = 50.0  # 基础分
            
            sector_rank = data.get('sector_rank', 50)
            sector_momentum = data.get('sector_momentum', 0)
            sector_rotation_speed = data.get('sector_rotation_speed', 0.2)
            
            # 行业排名评分
            if sector_rank <= 5:
                score += 20  # 行业龙头
            elif sector_rank <= 10:
                score += 15  # 行业前10
            elif sector_rank <= 20:
                score += 10  # 行业前20
            elif sector_rank <= 50:
                score += 5   # 行业前50
            else:
                score -= 10  # 行业排名靠后
            
            # 行业动量评分
            if sector_momentum > 0.2:
                score += 15  # 强势行业
            elif sector_momentum > 0.1:
                score += 10  # 较强行业
            elif sector_momentum > 0:
                score += 5   # 正动量
            elif sector_momentum < -0.1:
                score -= 15  # 弱势行业
            elif sector_momentum < 0:
                score -= 10  # 负动量
            
            # 板块轮动速度评分
            if sector_rotation_speed > 0.5:
                score += 10  # 轮动活跃
            elif sector_rotation_speed > 0.3:
                score += 5   # 轮动正常
            elif sector_rotation_speed < 0.1:
                score -= 5   # 轮动缓慢
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算板块轮动评分失败: {e}")
            return 50.0
    
    def _calculate_market_structure_score(self, data: Dict[str, Any]) -> float:
        """计算市场结构评分"""
        try:
            score = 50.0  # 基础分
            
            market_cap_rank = data.get('market_cap_rank', 100)
            market_cap_category = data.get('market_cap_category', 'mid')
            index_weight = data.get('index_weight', 0)
            market_concentration = data.get('market_concentration', 0.3)
            
            # 市值排名评分
            if market_cap_rank <= 10:
                score += 20  # 超大市值
            elif market_cap_rank <= 50:
                score += 15  # 大市值
            elif market_cap_rank <= 200:
                score += 10  # 中市值
            elif market_cap_rank <= 500:
                score += 5   # 中小市值
            else:
                score -= 5   # 小市值
            
            # 市值分类评分
            if market_cap_category == 'large':
                score += 10
            elif market_cap_category == 'mid':
                score += 5
            elif market_cap_category == 'small':
                score -= 5
            
            # 指数权重评分
            if index_weight > 0.05:
                score += 15  # 重要指数成分股
            elif index_weight > 0.02:
                score += 10  # 指数成分股
            elif index_weight > 0.01:
                score += 5   # 小权重成分股
            
            # 市场集中度评分
            if market_concentration < 0.2:
                score += 10  # 分散化好
            elif market_concentration < 0.3:
                score += 5   # 分散化一般
            elif market_concentration > 0.5:
                score -= 10  # 过度集中
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算市场结构评分失败: {e}")
            return 50.0
    
    def _calculate_market_breadth_score(self, data: Dict[str, Any]) -> float:
        """计算市场宽度评分"""
        try:
            score = 50.0  # 基础分
            
            market_breadth = data.get('market_breadth', 0.5)
            new_highs = data.get('new_highs', 0)
            new_lows = data.get('new_lows', 0)
            
            # 市场宽度评分
            if market_breadth > 0.7:
                score += 20  # 市场宽度良好
            elif market_breadth > 0.6:
                score += 15  # 市场宽度较好
            elif market_breadth > 0.5:
                score += 10  # 市场宽度一般
            elif market_breadth > 0.4:
                score += 5   # 市场宽度偏弱
            else:
                score -= 15  # 市场宽度差
            
            # 新高新低评分
            if new_highs > 50:
                score += 15
            elif new_highs > 20:
                score += 10
            elif new_highs > 10:
                score += 5
            elif new_lows > 50:
                score -= 20
            elif new_lows > 20:
                score -= 15
            elif new_lows > 10:
                score -= 10
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算市场宽度评分失败: {e}")
            return 50.0
    
    def _calculate_market_overall_score(self, market_sentiment_score: float,
                                      sector_rotation_score: float,
                                      market_structure_score: float,
                                      market_breadth_score: float) -> float:
        """计算市场面综合评分"""
        try:
            # 权重配置
            weights = {
                'market_sentiment': 0.30,    # 市场情绪权重30%
                'sector_rotation': 0.25,     # 板块轮动权重25%
                'market_structure': 0.25,    # 市场结构权重25%
                'market_breadth': 0.20       # 市场宽度权重20%
            }
            
            overall_score = (
                market_sentiment_score * weights['market_sentiment'] +
                sector_rotation_score * weights['sector_rotation'] +
                market_structure_score * weights['market_structure'] +
                market_breadth_score * weights['market_breadth']
            )
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"计算市场面综合评分失败: {e}")
            return 50.0

class MacroAnalyzer:
    """宏观面分析器"""

    def __init__(self):
        self.settings = get_settings()
        self.mock_manager = get_mock_data_manager()
    
    def analyze_macro(self, code: str) -> Optional[MacroAnalysis]:
        """分析宏观面"""
        try:
            logger.info(f"开始分析股票 {code} 宏观面")
            
            # 获取宏观面数据
            macro_data = self._get_macro_data(code)
            if not macro_data:
                logger.warning(f"股票 {code} 宏观面数据不足")
                return None
            
            # 计算各维度评分
            interest_rate_score = self._calculate_interest_rate_score(macro_data)
            inflation_score = self._calculate_inflation_score(macro_data)
            policy_score = self._calculate_policy_score(macro_data)
            economic_growth_score = self._calculate_economic_growth_score(macro_data)
            
            # 计算综合评分
            overall_score = self._calculate_macro_overall_score(
                interest_rate_score, inflation_score, policy_score, economic_growth_score
            )
            
            return MacroAnalysis(
                interest_rate_score=interest_rate_score,
                inflation_score=inflation_score,
                policy_score=policy_score,
                economic_growth_score=economic_growth_score,
                overall_score=overall_score,
                details=macro_data
            )
            
        except Exception as e:
            logger.error(f"分析宏观面失败: {e}")
            return None
    
    def _get_macro_data(self, code: str) -> Dict[str, Any]:
        """
        获取宏观面数据
        使用模拟数据管理器获取一致性数据
        """
        try:
            # 宏观数据通常不依赖具体股票，使用固定种子
            macro_code = "MACRO_DATA"

            # 使用模拟数据管理器获取数据
            mock_data = self.mock_manager.get_macro_data(macro_code)

            if not mock_data:
                logger.warning("无法获取宏观面数据")
                return {}

            # 记录模拟数据使用情况
            self.mock_manager.log_mock_usage(code, "macro", list(mock_data.keys()))

            logger.debug(f"宏观面数据: GDP增长率={mock_data['gdp_growth']:.1f}%, "
                        f"基准利率={mock_data['benchmark_rate']:.2f}%, "
                        f"CPI={mock_data['cpi_yoy']:.1f}%")

            return mock_data

        except Exception as e:
            logger.error(f"获取宏观面数据失败: {e}")
            return {}
    
    def _calculate_interest_rate_score(self, data: Dict[str, Any]) -> float:
        """计算利率环境评分"""
        try:
            score = 50.0  # 基础分
            
            benchmark_rate = data.get('benchmark_rate', 3.85)
            lending_rate = data.get('lending_rate', 4.35)
            deposit_rate = data.get('deposit_rate', 1.50)
            interbank_rate = data.get('interbank_rate', 2.10)
            bond_yield_10y = data.get('bond_yield_10y', 2.85)
            
            # 基准利率评分
            if benchmark_rate < 2.0:
                score += 20  # 极低利率环境
            elif benchmark_rate < 3.0:
                score += 15  # 低利率环境
            elif benchmark_rate < 4.0:
                score += 10  # 适中利率环境
            elif benchmark_rate < 5.0:
                score += 5   # 较高利率环境
            else:
                score -= 10  # 高利率环境
            
            # 贷款基准利率评分
            if lending_rate < 3.0:
                score += 15  # 低融资成本
            elif lending_rate < 4.5:
                score += 10  # 适中融资成本
            elif lending_rate < 6.0:
                score += 5   # 较高融资成本
            else:
                score -= 10  # 高融资成本
            
            # 存款基准利率评分
            if deposit_rate < 1.0:
                score += 10  # 低储蓄收益
            elif deposit_rate < 2.0:
                score += 5   # 适中储蓄收益
            elif deposit_rate > 3.0:
                score -= 5   # 高储蓄收益
            
            # 同业拆借利率评分
            if interbank_rate < 1.5:
                score += 10  # 宽松流动性
            elif interbank_rate < 2.5:
                score += 5   # 适中流动性
            elif interbank_rate > 3.5:
                score -= 10  # 紧张流动性
            
            # 国债收益率评分
            if bond_yield_10y < 2.0:
                score += 10  # 低无风险利率
            elif bond_yield_10y < 3.0:
                score += 5   # 适中无风险利率
            elif bond_yield_10y > 4.0:
                score -= 10  # 高无风险利率
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算利率环境评分失败: {e}")
            return 50.0
    
    def _calculate_inflation_score(self, data: Dict[str, Any]) -> float:
        """计算通胀预期评分"""
        try:
            score = 50.0  # 基础分
            
            cpi_yoy = data.get('cpi_yoy', 2.5)
            ppi_yoy = data.get('ppi_yoy', 1.2)
            core_cpi_yoy = data.get('core_cpi_yoy', 2.1)
            inflation_expectation = data.get('inflation_expectation', 2.8)
            
            # CPI同比评分
            if cpi_yoy < 1.0:
                score -= 15  # 通缩风险
            elif cpi_yoy < 2.0:
                score += 10  # 温和通胀
            elif cpi_yoy < 3.0:
                score += 15  # 适度通胀
            elif cpi_yoy < 5.0:
                score += 5   # 较高通胀
            else:
                score -= 20  # 高通胀风险
            
            # PPI同比评分
            if ppi_yoy < 0:
                score -= 10  # 工业通缩
            elif ppi_yoy < 2.0:
                score += 10  # 温和工业通胀
            elif ppi_yoy < 5.0:
                score += 5   # 较高工业通胀
            else:
                score -= 15  # 高工业通胀
            
            # 核心CPI评分
            if core_cpi_yoy < 1.5:
                score -= 10  # 核心通胀过低
            elif core_cpi_yoy < 2.5:
                score += 10  # 核心通胀适中
            elif core_cpi_yoy > 3.5:
                score -= 10  # 核心通胀过高
            
            # 通胀预期评分
            if inflation_expectation < 2.0:
                score += 10  # 通胀预期温和
            elif inflation_expectation < 3.0:
                score += 5   # 通胀预期适中
            elif inflation_expectation > 4.0:
                score -= 15  # 通胀预期过高
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算通胀预期评分失败: {e}")
            return 50.0
    
    def _calculate_policy_score(self, data: Dict[str, Any]) -> float:
        """计算政策环境评分"""
        try:
            score = 50.0  # 基础分
            
            monetary_policy = data.get('monetary_policy', 0.6)
            fiscal_policy = data.get('fiscal_policy', 0.7)
            regulatory_policy = data.get('regulatory_policy', 0.5)
            industry_policy = data.get('industry_policy', 0.8)
            market_sentiment = data.get('market_sentiment', 0.65)
            
            # 货币政策评分
            if monetary_policy > 0.8:
                score += 20  # 极度宽松
            elif monetary_policy > 0.6:
                score += 15  # 宽松
            elif monetary_policy > 0.4:
                score += 10  # 中性
            elif monetary_policy > 0.2:
                score += 5   # 偏紧
            else:
                score -= 15  # 紧缩
            
            # 财政政策评分
            if fiscal_policy > 0.8:
                score += 15  # 极度积极
            elif fiscal_policy > 0.6:
                score += 10  # 积极
            elif fiscal_policy > 0.4:
                score += 5   # 中性
            else:
                score -= 10  # 紧缩
            
            # 监管政策评分
            if regulatory_policy < 0.3:
                score += 15  # 宽松监管
            elif regulatory_policy < 0.5:
                score += 10  # 适中监管
            elif regulatory_policy < 0.7:
                score += 5   # 较严监管
            else:
                score -= 15  # 严格监管
            
            # 产业政策评分
            if industry_policy > 0.8:
                score += 15  # 强支持
            elif industry_policy > 0.6:
                score += 10  # 支持
            elif industry_policy > 0.4:
                score += 5   # 一般
            else:
                score -= 10  # 不支持
            
            # 市场情绪评分
            if market_sentiment > 0.8:
                score += 10  # 极度乐观
            elif market_sentiment > 0.6:
                score += 5   # 乐观
            elif market_sentiment < 0.3:
                score -= 10  # 悲观
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算政策环境评分失败: {e}")
            return 50.0
    
    def _calculate_economic_growth_score(self, data: Dict[str, Any]) -> float:
        """计算经济增长评分"""
        try:
            score = 50.0  # 基础分
            
            gdp_growth = data.get('gdp_growth', 5.2)
            pmi = data.get('pmi', 51.5)
            industrial_production = data.get('industrial_production', 4.8)
            retail_sales = data.get('retail_sales', 7.5)
            fixed_investment = data.get('fixed_investment', 6.2)
            
            # GDP增长率评分
            if gdp_growth > 7.0:
                score += 20  # 高速增长
            elif gdp_growth > 5.0:
                score += 15  # 中高速增长
            elif gdp_growth > 3.0:
                score += 10  # 中速增长
            elif gdp_growth > 1.0:
                score += 5   # 低速增长
            else:
                score -= 20  # 负增长
            
            # PMI指数评分
            if pmi > 55:
                score += 15  # 强扩张
            elif pmi > 50:
                score += 10  # 扩张
            elif pmi > 45:
                score += 5   # 收缩
            else:
                score -= 15  # 强收缩
            
            # 工业增加值增长率评分
            if industrial_production > 8.0:
                score += 15  # 高速增长
            elif industrial_production > 5.0:
                score += 10  # 中高速增长
            elif industrial_production > 2.0:
                score += 5   # 中速增长
            else:
                score -= 10  # 低速增长
            
            # 社会消费品零售总额增长率评分
            if retail_sales > 10.0:
                score += 15  # 高速增长
            elif retail_sales > 7.0:
                score += 10  # 中高速增长
            elif retail_sales > 4.0:
                score += 5   # 中速增长
            else:
                score -= 10  # 低速增长
            
            # 固定资产投资增长率评分
            if fixed_investment > 10.0:
                score += 15  # 高速增长
            elif fixed_investment > 6.0:
                score += 10  # 中高速增长
            elif fixed_investment > 3.0:
                score += 5   # 中速增长
            else:
                score -= 10  # 低速增长
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算经济增长评分失败: {e}")
            return 50.0
    
    def _calculate_macro_overall_score(self, interest_rate_score: float,
                                     inflation_score: float,
                                     policy_score: float,
                                     economic_growth_score: float) -> float:
        """计算宏观面综合评分"""
        try:
            # 权重配置
            weights = {
                'interest_rate': 0.25,      # 利率环境权重25%
                'inflation': 0.20,          # 通胀预期权重20%
                'policy': 0.30,             # 政策环境权重30%
                'economic_growth': 0.25     # 经济增长权重25%
            }
            
            overall_score = (
                interest_rate_score * weights['interest_rate'] +
                inflation_score * weights['inflation'] +
                policy_score * weights['policy'] +
                economic_growth_score * weights['economic_growth']
            )
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"计算宏观面综合评分失败: {e}")
            return 50.0

class BehavioralAnalyzer:
    """行为面分析器"""

    def __init__(self):
        self.settings = get_settings()
        self.mock_manager = get_mock_data_manager()
    
    def analyze_behavioral(self, code: str) -> Optional[BehavioralAnalysis]:
        """分析行为面"""
        try:
            logger.info(f"开始分析股票 {code} 行为面")
            
            # 获取行为面数据
            behavioral_data = self._get_behavioral_data(code)
            if not behavioral_data:
                logger.warning(f"股票 {code} 行为面数据不足")
                return None
            
            # 计算各维度评分
            herding_score = self._calculate_herding_score(behavioral_data)
            overreaction_score = self._calculate_overreaction_score(behavioral_data)
            anchoring_score = self._calculate_anchoring_score(behavioral_data)
            disposition_score = self._calculate_disposition_score(behavioral_data)
            
            # 计算综合评分
            overall_score = self._calculate_behavioral_overall_score(
                herding_score, overreaction_score, anchoring_score, disposition_score
            )
            
            return BehavioralAnalysis(
                herding_score=herding_score,
                overreaction_score=overreaction_score,
                anchoring_score=anchoring_score,
                disposition_score=disposition_score,
                overall_score=overall_score,
                details=behavioral_data
            )
            
        except Exception as e:
            logger.error(f"分析行为面失败: {e}")
            return None
    
    def _get_behavioral_data(self, code: str) -> Dict[str, Any]:
        """
        获取行为面数据
        使用模拟数据管理器获取一致性数据
        """
        try:
            # 使用模拟数据管理器获取数据
            mock_data = self.mock_manager.get_behavioral_data(code)

            if not mock_data:
                logger.warning(f"股票 {code} 无法获取行为面数据")
                return {}

            # 记录模拟数据使用情况
            self.mock_manager.log_mock_usage(code, "behavioral", list(mock_data.keys()))

            logger.debug(f"股票 {code} 行为面数据: 机构羊群效应={mock_data['institutional_herding']:.2f}, "
                        f"散户羊群效应={mock_data['retail_herding']:.2f}, "
                        f"价格动量={mock_data['price_momentum']:.2f}")

            return mock_data

        except Exception as e:
            logger.error(f"获取行为面数据失败: {e}")
            return {}
    
    def _calculate_herding_score(self, data: Dict[str, Any]) -> float:
        """计算羊群效应评分"""
        try:
            score = 50.0  # 基础分
            
            volume_concentration = data.get('volume_concentration', 0.3)
            price_momentum = data.get('price_momentum', 0.15)
            institutional_herding = data.get('institutional_herding', 0.4)
            retail_herding = data.get('retail_herding', 0.6)
            
            # 成交量集中度评分
            if volume_concentration < 0.2:
                score += 15  # 分散化好
            elif volume_concentration < 0.4:
                score += 10  # 分散化一般
            elif volume_concentration < 0.6:
                score += 5   # 集中度较高
            else:
                score -= 15  # 过度集中
            
            # 价格动量评分
            if price_momentum < 0.1:
                score += 15  # 动量温和
            elif price_momentum < 0.2:
                score += 10  # 动量适中
            elif price_momentum < 0.3:
                score += 5   # 动量较强
            else:
                score -= 10  # 动量过强
            
            # 机构羊群效应评分
            if institutional_herding < 0.3:
                score += 15  # 机构行为理性
            elif institutional_herding < 0.5:
                score += 10  # 机构行为一般
            elif institutional_herding < 0.7:
                score += 5   # 机构羊群效应较强
            else:
                score -= 15  # 机构羊群效应严重
            
            # 散户羊群效应评分
            if retail_herding < 0.4:
                score += 15  # 散户行为理性
            elif retail_herding < 0.6:
                score += 10  # 散户行为一般
            elif retail_herding < 0.8:
                score += 5   # 散户羊群效应较强
            else:
                score -= 15  # 散户羊群效应严重
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算羊群效应评分失败: {e}")
            return 50.0
    
    def _calculate_overreaction_score(self, data: Dict[str, Any]) -> float:
        """计算过度反应评分"""
        try:
            score = 50.0  # 基础分
            
            price_volatility = data.get('price_volatility', 0.25)
            volume_spike = data.get('volume_spike', 0.2)
            news_impact = data.get('news_impact', 0.3)
            sentiment_swing = data.get('sentiment_swing', 0.4)
            
            # 价格波动率评分
            if price_volatility < 0.15:
                score += 15  # 波动率低
            elif price_volatility < 0.25:
                score += 10  # 波动率适中
            elif price_volatility < 0.35:
                score += 5   # 波动率较高
            else:
                score -= 15  # 波动率过高
            
            # 成交量异常评分
            if volume_spike < 0.1:
                score += 15  # 成交量正常
            elif volume_spike < 0.2:
                score += 10  # 成交量略高
            elif volume_spike < 0.3:
                score += 5   # 成交量较高
            else:
                score -= 15  # 成交量异常
            
            # 新闻影响度评分
            if news_impact < 0.2:
                score += 15  # 新闻影响小
            elif news_impact < 0.4:
                score += 10  # 新闻影响适中
            elif news_impact < 0.6:
                score += 5   # 新闻影响较大
            else:
                score -= 15  # 新闻影响过大
            
            # 情绪波动评分
            if sentiment_swing < 0.3:
                score += 15  # 情绪稳定
            elif sentiment_swing < 0.5:
                score += 10  # 情绪波动适中
            elif sentiment_swing < 0.7:
                score += 5   # 情绪波动较大
            else:
                score -= 15  # 情绪波动剧烈
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算过度反应评分失败: {e}")
            return 50.0
    
    def _calculate_anchoring_score(self, data: Dict[str, Any]) -> float:
        """计算锚定效应评分"""
        try:
            score = 50.0  # 基础分
            
            price_anchor = data.get('price_anchor', 0.5)
            volume_anchor = data.get('volume_anchor', 0.4)
            support_resistance_anchor = data.get('support_resistance_anchor', 0.6)
            historical_high_low = data.get('historical_high_low', 0.7)
            
            # 价格锚定评分
            if price_anchor < 0.3:
                score += 15  # 锚定效应弱
            elif price_anchor < 0.5:
                score += 10  # 锚定效应适中
            elif price_anchor < 0.7:
                score += 5   # 锚定效应较强
            else:
                score -= 15  # 锚定效应严重
            
            # 成交量锚定评分
            if volume_anchor < 0.3:
                score += 15  # 成交量锚定弱
            elif volume_anchor < 0.5:
                score += 10  # 成交量锚定适中
            elif volume_anchor < 0.7:
                score += 5   # 成交量锚定较强
            else:
                score -= 15  # 成交量锚定严重
            
            # 支撑阻力锚定评分
            if support_resistance_anchor < 0.4:
                score += 15  # 支撑阻力锚定弱
            elif support_resistance_anchor < 0.6:
                score += 10  # 支撑阻力锚定适中
            elif support_resistance_anchor < 0.8:
                score += 5   # 支撑阻力锚定较强
            else:
                score -= 15  # 支撑阻力锚定严重
            
            # 历史高低点锚定评分
            if historical_high_low < 0.5:
                score += 15  # 历史锚定弱
            elif historical_high_low < 0.7:
                score += 10  # 历史锚定适中
            elif historical_high_low < 0.9:
                score += 5   # 历史锚定较强
            else:
                score -= 15  # 历史锚定严重
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算锚定效应评分失败: {e}")
            return 50.0
    
    def _calculate_disposition_score(self, data: Dict[str, Any]) -> float:
        """计算处置效应评分"""
        try:
            score = 50.0  # 基础分
            
            profit_taking_ratio = data.get('profit_taking_ratio', 0.3)
            loss_holding_ratio = data.get('loss_holding_ratio', 0.7)
            gain_realization_speed = data.get('gain_realization_speed', 0.4)
            loss_realization_speed = data.get('loss_realization_speed', 0.2)
            
            # 获利了结比例评分
            if profit_taking_ratio < 0.2:
                score += 15  # 获利了结理性
            elif profit_taking_ratio < 0.4:
                score += 10  # 获利了结适中
            elif profit_taking_ratio < 0.6:
                score += 5   # 获利了结较快
            else:
                score -= 15  # 获利了结过快
            
            # 亏损持有比例评分
            if loss_holding_ratio < 0.5:
                score += 15  # 亏损处理理性
            elif loss_holding_ratio < 0.7:
                score += 10  # 亏损处理适中
            elif loss_holding_ratio < 0.9:
                score += 5   # 亏损持有较多
            else:
                score -= 15  # 亏损持有严重
            
            # 盈利实现速度评分
            if gain_realization_speed < 0.3:
                score += 15  # 盈利实现理性
            elif gain_realization_speed < 0.5:
                score += 10  # 盈利实现适中
            elif gain_realization_speed < 0.7:
                score += 5   # 盈利实现较快
            else:
                score -= 15  # 盈利实现过快
            
            # 亏损实现速度评分
            if loss_realization_speed < 0.1:
                score -= 15  # 亏损实现过慢
            elif loss_realization_speed < 0.3:
                score += 5   # 亏损实现较慢
            elif loss_realization_speed < 0.5:
                score += 10  # 亏损实现适中
            else:
                score += 15  # 亏损实现理性
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算处置效应评分失败: {e}")
            return 50.0
    
    def _calculate_behavioral_overall_score(self, herding_score: float,
                                          overreaction_score: float,
                                          anchoring_score: float,
                                          disposition_score: float) -> float:
        """计算行为面综合评分"""
        try:
            # 权重配置
            weights = {
                'herding': 0.30,        # 羊群效应权重30%
                'overreaction': 0.25,   # 过度反应权重25%
                'anchoring': 0.25,      # 锚定效应权重25%
                'disposition': 0.20     # 处置效应权重20%
            }
            
            overall_score = (
                herding_score * weights['herding'] +
                overreaction_score * weights['overreaction'] +
                anchoring_score * weights['anchoring'] +
                disposition_score * weights['disposition']
            )
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"计算行为面综合评分失败: {e}")
            return 50.0

class TechnicalIndicators:
    """技术指标计算类"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # 技术指标参数配置
        self.params = {
            'ma_periods': [5, 10, 20, 30, 60, 120],  # 均线周期
            'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9,  # MACD参数
            'rsi_period': 14,  # RSI周期
            'kdj_period': 9,   # KDJ周期
            'bollinger_period': 20, 'bollinger_std': 2,  # 布林带参数
            'atr_period': 14,  # ATR周期
            'cci_period': 14,  # CCI周期
            'williams_period': 14,  # 威廉指标周期
            'stoch_period': 14,  # 随机指标周期
            'adx_period': 14,  # ADX周期
            'obv_period': 10,  # OBV周期
            'volume_ma_period': 20,  # 成交量均线周期
        }
    
    def calculate_ma_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算均线信号"""
        try:
            if len(df) < 30:
                return {}
            
            close = df['close'].astype(float)
            
            # 计算多条均线
            ma5 = close.rolling(5).mean()
            ma10 = close.rolling(10).mean()
            ma20 = close.rolling(20).mean()
            ma30 = close.rolling(30).mean()
            ma60 = close.rolling(60).mean()
            
            current = df.iloc[-1]
            prev = df.iloc[-2]
            
            signals = {}
            
            # 均线多头排列
            if (ma5.iloc[-1] > ma10.iloc[-1] > ma20.iloc[-1] > ma30.iloc[-1]):
                signals['ma_bullish'] = True
                signals['ma_strength'] = 1.0
            # 均线空头排列
            elif (ma5.iloc[-1] < ma10.iloc[-1] < ma20.iloc[-1] < ma30.iloc[-1]):
                signals['ma_bullish'] = False
                signals['ma_strength'] = -1.0
            else:
                signals['ma_bullish'] = None
                signals['ma_strength'] = 0.0
            
            # 金叉死叉信号
            if (ma5.iloc[-1] > ma10.iloc[-1] and ma5.iloc[-2] <= ma10.iloc[-2]):
                signals['golden_cross'] = True
            elif (ma5.iloc[-1] < ma10.iloc[-1] and ma5.iloc[-2] >= ma10.iloc[-2]):
                signals['death_cross'] = True
            else:
                signals['golden_cross'] = False
                signals['death_cross'] = False
            
            # 价格相对均线位置
            signals['price_vs_ma5'] = current['close'] / ma5.iloc[-1] - 1
            signals['price_vs_ma10'] = current['close'] / ma10.iloc[-1] - 1
            signals['price_vs_ma20'] = current['close'] / ma20.iloc[-1] - 1
            
            return signals
            
        except Exception as e:
            logger.error(f"计算均线信号失败: {e}")
            return {}
    
    def calculate_macd_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算MACD信号"""
        try:
            if len(df) < 50:
                return {}
            
            close = df['close'].astype(float).values
            
            # 计算MACD
            macd, signal, hist = talib.MACD(
                close, 
                fastperiod=12, 
                slowperiod=26, 
                signalperiod=9
            )
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(macd) | np.isnan(signal))
            if np.sum(valid_indices) < 2:
                return signals
            
            macd_clean = macd[valid_indices]
            signal_clean = signal[valid_indices]
            hist_clean = hist[valid_indices]
            
            if len(macd_clean) < 2:
                return signals
            
            # 金叉死叉
            if (macd_clean[-1] > signal_clean[-1] and 
                macd_clean[-2] <= signal_clean[-2]):
                signals['macd_golden_cross'] = True
                signals['macd_signal'] = 1
            elif (macd_clean[-1] < signal_clean[-1] and 
                  macd_clean[-2] >= signal_clean[-2]):
                signals['macd_death_cross'] = True
                signals['macd_signal'] = -1
            else:
                signals['macd_golden_cross'] = False
                signals['macd_death_cross'] = False
                signals['macd_signal'] = 0
            
            # MACD柱状图变化
            if len(hist_clean) >= 3:
                signals['macd_hist_trend'] = (
                    1 if hist_clean[-1] > hist_clean[-2] > hist_clean[-3]
                    else -1 if hist_clean[-1] < hist_clean[-2] < hist_clean[-3]
                    else 0
                )
            
            # MACD背离检测
            signals['macd_divergence'] = self._detect_macd_divergence(df, macd_clean)
            
            return signals
            
        except Exception as e:
            logger.error(f"计算MACD信号失败: {e}")
            return {}
    
    def calculate_rsi_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算RSI信号"""
        try:
            if len(df) < 30:
                return {}
            
            close = df['close'].astype(float).values
            rsi = talib.RSI(close, timeperiod=14)
            
            signals = {}
            
            # 去除NaN值
            valid_rsi = rsi[~np.isnan(rsi)]
            if len(valid_rsi) == 0:
                return signals
            
            current_rsi = valid_rsi[-1]
            
            # RSI超买超卖
            if current_rsi > 80:
                signals['rsi_overbought'] = True
                signals['rsi_signal'] = -1
            elif current_rsi < 20:
                signals['rsi_oversold'] = True
                signals['rsi_signal'] = 1
            else:
                signals['rsi_overbought'] = False
                signals['rsi_oversold'] = False
                signals['rsi_signal'] = 0
            
            # RSI趋势
            if len(valid_rsi) >= 3:
                if valid_rsi[-1] > valid_rsi[-2] > valid_rsi[-3]:
                    signals['rsi_trend'] = 1
                elif valid_rsi[-1] < valid_rsi[-2] < valid_rsi[-3]:
                    signals['rsi_trend'] = -1
                else:
                    signals['rsi_trend'] = 0
            
            signals['rsi_value'] = current_rsi
            
            return signals
            
        except Exception as e:
            logger.error(f"计算RSI信号失败: {e}")
            return {}
    
    def calculate_volume_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量信号"""
        try:
            if len(df) < 20:
                return {}
            
            signals = {}
            
            # 计算成交量均线
            volume = df['volume'].astype(float)
            volume_ma5 = volume.rolling(5).mean()
            volume_ma10 = volume.rolling(10).mean()
            volume_ma20 = volume.rolling(20).mean()
            
            current = df.iloc[-1]
            
            # 放量信号
            if volume.iloc[-1] > volume_ma5.iloc[-1] * 2:
                signals['volume_surge'] = True
                signals['volume_ratio'] = volume.iloc[-1] / volume_ma5.iloc[-1]
            else:
                signals['volume_surge'] = False
                signals['volume_ratio'] = volume.iloc[-1] / volume_ma5.iloc[-1]
            
            # 量价配合
            price_change = current['pctChg']
            if price_change > 0 and signals['volume_surge']:
                signals['volume_price_positive'] = True
            elif price_change < 0 and signals['volume_surge']:
                signals['volume_price_negative'] = True
            else:
                signals['volume_price_positive'] = False
                signals['volume_price_negative'] = False
            
            # OBV指标
            obv = talib.OBV(df['close'].astype(float).values, 
                           df['volume'].astype(float).values)
            if len(obv) >= 2:
                signals['obv_trend'] = 1 if obv[-1] > obv[-2] else -1
            
            return signals
            
        except Exception as e:
            logger.error(f"计算成交量信号失败: {e}")
            return {}
    
    def calculate_kdj_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算KDJ信号"""
        try:
            if len(df) < 30:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            k, d = talib.STOCH(high, low, close, 
                              fastk_period=9, 
                              slowk_period=3, 
                              slowk_matype=0,
                              slowd_period=3, 
                              slowd_matype=0)
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(k) | np.isnan(d))
            if np.sum(valid_indices) < 2:
                return signals
            
            k_clean = k[valid_indices]
            d_clean = d[valid_indices]
            
            if len(k_clean) < 2:
                return signals
            
            # 计算J值
            j = 3 * k_clean - 2 * d_clean
            
            current_k = k_clean[-1]
            current_d = d_clean[-1]
            current_j = j[-1]
            
            # KDJ金叉死叉
            if (current_k > current_d and k_clean[-2] <= d_clean[-2]):
                signals['kdj_golden_cross'] = True
                signals['kdj_signal'] = 1
            elif (current_k < current_d and k_clean[-2] >= d_clean[-2]):
                signals['kdj_death_cross'] = True
                signals['kdj_signal'] = -1
            else:
                signals['kdj_golden_cross'] = False
                signals['kdj_death_cross'] = False
                signals['kdj_signal'] = 0
            
            # 超买超卖
            if current_k > 80 and current_d > 80:
                signals['kdj_overbought'] = True
            elif current_k < 20 and current_d < 20:
                signals['kdj_oversold'] = True
            else:
                signals['kdj_overbought'] = False
                signals['kdj_oversold'] = False
            
            signals['k_value'] = current_k
            signals['d_value'] = current_d
            signals['j_value'] = current_j
            
            return signals
            
        except Exception as e:
            logger.error(f"计算KDJ信号失败: {e}")
            return {}
    
    def calculate_support_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算支撑阻力位"""
        try:
            if len(df) < 60:
                return {}
            
            high = df['high'].astype(float)
            low = df['low'].astype(float)
            close = df['close'].astype(float)
            current_price = close.iloc[-1]
            
            signals = {}
            
            # 计算近期高低点
            recent_high = high.tail(20).max()
            recent_low = low.tail(20).min()
            
            # 支撑位和阻力位
            resistance_levels = []
            support_levels = []
            
            # 寻找阻力位（近期高点附近）
            for i in range(len(high) - 20, len(high)):
                if high.iloc[i] >= recent_high * 0.98:
                    resistance_levels.append(high.iloc[i])
            
            # 寻找支撑位（近期低点附近）
            for i in range(len(low) - 20, len(low)):
                if low.iloc[i] <= recent_low * 1.02:
                    support_levels.append(low.iloc[i])
            
            # 去重并排序
            resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
            support_levels = sorted(list(set(support_levels)))
            
            signals['nearest_resistance'] = resistance_levels[0] if resistance_levels else None
            signals['nearest_support'] = support_levels[0] if support_levels else None
            
            # 价格位置判断
            if signals['nearest_resistance'] and signals['nearest_support']:
                resistance_distance = (signals['nearest_resistance'] - current_price) / current_price
                support_distance = (current_price - signals['nearest_support']) / current_price
                
                if resistance_distance < 0.05:  # 接近阻力位
                    signals['near_resistance'] = True
                elif support_distance < 0.05:  # 接近支撑位
                    signals['near_support'] = True
                else:
                    signals['near_resistance'] = False
                    signals['near_support'] = False
            
            return signals
            
        except Exception as e:
            logger.error(f"计算支撑阻力位失败: {e}")
            return {}
    
    def calculate_bollinger_bands(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算布林带信号"""
        try:
            if len(df) < self.params['bollinger_period']:
                return {}
            
            close = df['close'].astype(float).values
            period = self.params['bollinger_period']
            std_dev = self.params['bollinger_std']
            
            # 计算布林带
            upper, middle, lower = talib.BBANDS(
                close, 
                timeperiod=period, 
                nbdevup=std_dev, 
                nbdevdn=std_dev, 
                matype=0
            )
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(upper) | np.isnan(middle) | np.isnan(lower))
            if np.sum(valid_indices) < 2:
                return signals
            
            upper_clean = upper[valid_indices]
            middle_clean = middle[valid_indices]
            lower_clean = lower[valid_indices]
            
            if len(upper_clean) < 2:
                return signals
            
            current_price = close[-1]
            current_upper = upper_clean[-1]
            current_middle = middle_clean[-1]
            current_lower = lower_clean[-1]
            
            # 价格位置判断
            if current_price >= current_upper:
                signals['bb_upper_breakout'] = True
                signals['bb_position'] = 'above_upper'
            elif current_price <= current_lower:
                signals['bb_lower_breakout'] = True
                signals['bb_position'] = 'below_lower'
            elif current_price > current_middle:
                signals['bb_position'] = 'above_middle'
            else:
                signals['bb_position'] = 'below_middle'
            
            # 布林带宽度（波动率）
            bb_width = (current_upper - current_lower) / current_middle
            signals['bb_width'] = bb_width
            
            # 布林带收缩/扩张
            if len(upper_clean) >= 5:
                prev_width = (upper_clean[-5] - lower_clean[-5]) / middle_clean[-5]
                if bb_width < prev_width * 0.8:
                    signals['bb_squeeze'] = True  # 布林带收缩
                elif bb_width > prev_width * 1.2:
                    signals['bb_expansion'] = True  # 布林带扩张
            
            return signals
            
        except Exception as e:
            logger.error(f"计算布林带信号失败: {e}")
            return {}
    
    def calculate_atr_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算ATR（平均真实波幅）信号"""
        try:
            if len(df) < self.params['atr_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算ATR
            atr = talib.ATR(high, low, close, timeperiod=self.params['atr_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_atr = atr[~np.isnan(atr)]
            if len(valid_atr) < 2:
                return signals
            
            current_atr = valid_atr[-1]
            prev_atr = valid_atr[-2]
            
            # ATR变化趋势
            if current_atr > prev_atr:
                signals['atr_increasing'] = True
                signals['atr_trend'] = 1
            elif current_atr < prev_atr:
                signals['atr_decreasing'] = True
                signals['atr_trend'] = -1
            else:
                signals['atr_trend'] = 0
            
            # ATR相对水平
            atr_ma = np.mean(valid_atr[-20:]) if len(valid_atr) >= 20 else np.mean(valid_atr)
            if current_atr > atr_ma * 1.5:
                signals['atr_high_volatility'] = True
            elif current_atr < atr_ma * 0.5:
                signals['atr_low_volatility'] = True
            
            signals['atr_value'] = current_atr
            signals['atr_ratio'] = current_atr / atr_ma if atr_ma > 0 else 1.0
            
            return signals
            
        except Exception as e:
            logger.error(f"计算ATR信号失败: {e}")
            return {}
    
    def calculate_cci_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算CCI（商品通道指数）信号"""
        try:
            if len(df) < self.params['cci_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算CCI
            cci = talib.CCI(high, low, close, timeperiod=self.params['cci_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_cci = cci[~np.isnan(cci)]
            if len(valid_cci) < 2:
                return signals
            
            current_cci = valid_cci[-1]
            prev_cci = valid_cci[-2]
            
            # CCI超买超卖
            if current_cci > 100:
                signals['cci_overbought'] = True
                signals['cci_signal'] = -1
            elif current_cci < -100:
                signals['cci_oversold'] = True
                signals['cci_signal'] = 1
            else:
                signals['cci_signal'] = 0
            
            # CCI趋势
            if current_cci > prev_cci:
                signals['cci_trend'] = 1
            elif current_cci < prev_cci:
                signals['cci_trend'] = -1
            else:
                signals['cci_trend'] = 0
            
            # CCI背离检测
            if len(valid_cci) >= 10:
                recent_highs = []
                recent_lows = []
                price_highs = []
                price_lows = []
                
                # 确保close数组长度匹配
                close_array = close[-len(valid_cci):]
                
                for i in range(len(valid_cci) - 10, len(valid_cci)):
                    if i > 0 and i < len(valid_cci) - 1:
                        if valid_cci[i] > valid_cci[i-1] and valid_cci[i] > valid_cci[i+1]:
                            recent_highs.append(valid_cci[i])
                            price_highs.append(close_array[i])
                        elif valid_cci[i] < valid_cci[i-1] and valid_cci[i] < valid_cci[i+1]:
                            recent_lows.append(valid_cci[i])
                            price_lows.append(close_array[i])
                
                if len(recent_highs) >= 2 and len(price_highs) >= 2:
                    if recent_highs[-1] < recent_highs[-2] and price_highs[-1] > price_highs[-2]:
                        signals['cci_bearish_divergence'] = True
                
                if len(recent_lows) >= 2 and len(price_lows) >= 2:
                    if recent_lows[-1] > recent_lows[-2] and price_lows[-1] < price_lows[-2]:
                        signals['cci_bullish_divergence'] = True
            
            signals['cci_value'] = current_cci
            
            return signals
            
        except Exception as e:
            logger.error(f"计算CCI信号失败: {e}")
            return {}
    
    def calculate_williams_r(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算威廉指标信号"""
        try:
            if len(df) < self.params['williams_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算威廉指标
            williams_r = talib.WILLR(high, low, close, timeperiod=self.params['williams_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_wr = williams_r[~np.isnan(williams_r)]
            if len(valid_wr) < 2:
                return signals
            
            current_wr = valid_wr[-1]
            prev_wr = valid_wr[-2]
            
            # 威廉指标超买超卖
            if current_wr > -20:
                signals['williams_overbought'] = True
                signals['williams_signal'] = -1
            elif current_wr < -80:
                signals['williams_oversold'] = True
                signals['williams_signal'] = 1
            else:
                signals['williams_signal'] = 0
            
            # 威廉指标趋势
            if current_wr > prev_wr:
                signals['williams_trend'] = 1
            elif current_wr < prev_wr:
                signals['williams_trend'] = -1
            else:
                signals['williams_trend'] = 0
            
            signals['williams_value'] = current_wr
            
            return signals
            
        except Exception as e:
            logger.error(f"计算威廉指标失败: {e}")
            return {}
    
    def calculate_stochastic_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算随机指标信号"""
        try:
            if len(df) < self.params['stoch_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算随机指标
            slowk, slowd = talib.STOCH(
                high, low, close,
                fastk_period=self.params['stoch_period'],
                slowk_period=3,
                slowk_matype=0,
                slowd_period=3,
                slowd_matype=0
            )
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(slowk) | np.isnan(slowd))
            if np.sum(valid_indices) < 2:
                return signals
            
            slowk_clean = slowk[valid_indices]
            slowd_clean = slowd[valid_indices]
            
            if len(slowk_clean) < 2:
                return signals
            
            current_k = slowk_clean[-1]
            current_d = slowd_clean[-1]
            prev_k = slowk_clean[-2]
            prev_d = slowd_clean[-2]
            
            # 随机指标超买超卖
            if current_k > 80 and current_d > 80:
                signals['stoch_overbought'] = True
                signals['stoch_signal'] = -1
            elif current_k < 20 and current_d < 20:
                signals['stoch_oversold'] = True
                signals['stoch_signal'] = 1
            else:
                signals['stoch_signal'] = 0
            
            # 随机指标金叉死叉
            if current_k > current_d and prev_k <= prev_d:
                signals['stoch_golden_cross'] = True
            elif current_k < current_d and prev_k >= prev_d:
                signals['stoch_death_cross'] = True
            
            signals['stoch_k'] = current_k
            signals['stoch_d'] = current_d
            
            return signals
            
        except Exception as e:
            logger.error(f"计算随机指标失败: {e}")
            return {}
    
    def calculate_adx_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算ADX（平均方向指数）信号"""
        try:
            if len(df) < self.params['adx_period']:
                return {}
            
            high = df['high'].astype(float).values
            low = df['low'].astype(float).values
            close = df['close'].astype(float).values
            
            # 计算ADX
            adx = talib.ADX(high, low, close, timeperiod=self.params['adx_period'])
            plus_di = talib.PLUS_DI(high, low, close, timeperiod=self.params['adx_period'])
            minus_di = talib.MINUS_DI(high, low, close, timeperiod=self.params['adx_period'])
            
            signals = {}
            
            # 去除NaN值
            valid_indices = ~(np.isnan(adx) | np.isnan(plus_di) | np.isnan(minus_di))
            if np.sum(valid_indices) < 2:
                return signals
            
            adx_clean = adx[valid_indices]
            plus_di_clean = plus_di[valid_indices]
            minus_di_clean = minus_di[valid_indices]
            
            if len(adx_clean) < 2:
                return signals
            
            current_adx = adx_clean[-1]
            current_plus_di = plus_di_clean[-1]
            current_minus_di = minus_di_clean[-1]
            
            # ADX趋势强度
            if current_adx > 25:
                signals['adx_strong_trend'] = True
            elif current_adx < 20:
                signals['adx_weak_trend'] = True
            
            # 方向判断
            if current_plus_di > current_minus_di:
                signals['adx_bullish'] = True
                signals['adx_direction'] = 1
            else:
                signals['adx_bearish'] = True
                signals['adx_direction'] = -1
            
            # ADX趋势变化
            if len(adx_clean) >= 3:
                if adx_clean[-1] > adx_clean[-2] > adx_clean[-3]:
                    signals['adx_increasing'] = True
                elif adx_clean[-1] < adx_clean[-2] < adx_clean[-3]:
                    signals['adx_decreasing'] = True
            
            signals['adx_value'] = current_adx
            signals['plus_di'] = current_plus_di
            signals['minus_di'] = current_minus_di
            
            return signals
            
        except Exception as e:
            logger.error(f"计算ADX信号失败: {e}")
            return {}
    
    def calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量相关指标"""
        try:
            if len(df) < 20:
                return {}
            
            volume = df['volume'].astype(float)
            close = df['close'].astype(float)
            high = df['high'].astype(float)
            low = df['low'].astype(float)
            
            signals = {}
            
            # 成交量均线
            volume_ma = volume.rolling(self.params['volume_ma_period']).mean()
            current_volume = volume.iloc[-1]
            current_volume_ma = volume_ma.iloc[-1]
            
            # 成交量比率
            volume_ratio = current_volume / current_volume_ma if current_volume_ma > 0 else 1.0
            signals['volume_ratio'] = volume_ratio
            
            # 放量信号
            if volume_ratio > 2.0:
                signals['volume_surge'] = True
            elif volume_ratio > 1.5:
                signals['volume_above_average'] = True
            elif volume_ratio < 0.5:
                signals['volume_below_average'] = True
            
            # OBV（能量潮）
            obv = talib.OBV(close.values, volume.values)
            if len(obv) >= 2:
                obv_trend = obv[-1] - obv[-2]
                if obv_trend > 0:
                    signals['obv_bullish'] = True
                else:
                    signals['obv_bearish'] = True
                
                # OBV趋势
                if len(obv) >= 10:
                    obv_ma = np.mean(obv[-10:])
                    if obv[-1] > obv_ma:
                        signals['obv_above_ma'] = True
                    else:
                        signals['obv_below_ma'] = True
            
            # 价量配合
            price_change = (close.iloc[-1] - close.iloc[-2]) / close.iloc[-2] if len(close) >= 2 else 0
            if price_change > 0 and volume_ratio > 1.5:
                signals['volume_price_positive'] = True
            elif price_change < 0 and volume_ratio > 1.5:
                signals['volume_price_negative'] = True
            
            # 成交量趋势
            if len(volume) >= 5:
                recent_volume_trend = volume.iloc[-5:].pct_change().mean()
                if recent_volume_trend > 0.1:
                    signals['volume_trend_increasing'] = True
                elif recent_volume_trend < -0.1:
                    signals['volume_trend_decreasing'] = True
            
            return signals
            
        except Exception as e:
            logger.error(f"计算成交量指标失败: {e}")
            return {}
    
    def calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算动量指标"""
        try:
            if len(df) < 30:
                return {}
            
            close = df['close'].astype(float)
            signals = {}
            
            # 价格动量
            for period in [5, 10, 20, 30]:
                momentum = (close.iloc[-1] - close.iloc[-period]) / close.iloc[-period] if len(close) >= period else 0
                signals[f'momentum_{period}d'] = momentum
            
            # 相对强弱
            for period in [5, 10, 20]:
                if len(close) >= period * 2:
                    gains = close.diff().where(close.diff() > 0, 0).rolling(period).mean()
                    losses = -close.diff().where(close.diff() < 0, 0).rolling(period).mean()
                    
                    # 确保数据有效
                    if not gains.empty and not losses.empty:
                        gain_value = gains.iloc[-1]
                        loss_value = losses.iloc[-1]
                        
                        if loss_value != 0:
                            rs = gain_value / loss_value
                            rsi_custom = 100 - (100 / (1 + rs))
                        else:
                            rsi_custom = 50
                        
                        signals[f'rsi_custom_{period}d'] = rsi_custom
            
            # 价格位置
            if len(close) >= 60:
                high_60 = close.rolling(60).max()
                low_60 = close.rolling(60).min()
                current_price = close.iloc[-1]
                
                if high_60.iloc[-1] > 0 and low_60.iloc[-1] > 0:
                    price_position = (current_price - low_60.iloc[-1]) / (high_60.iloc[-1] - low_60.iloc[-1])
                    signals['price_position_60d'] = price_position
                    
                    if price_position > 0.8:
                        signals['price_near_high'] = True
                    elif price_position < 0.2:
                        signals['price_near_low'] = True
            
            return signals
            
        except Exception as e:
            logger.error(f"计算动量指标失败: {e}")
            return {}
    
    def _detect_macd_divergence(self, df: pd.DataFrame, macd: np.ndarray) -> int:
        """检测MACD背离"""
        try:
            if len(df) < 30 or len(macd) < 10:
                return 0
            
            close = df['close'].astype(float).values
            recent_close = close[-10:]
            recent_macd = macd[-10:]
            
            # 价格创新高但MACD未创新高（顶背离）
            if (recent_close[-1] > recent_close[:-1].max() and 
                recent_macd[-1] < recent_macd[:-1].max()):
                return -1
            
            # 价格创新低但MACD未创新低（底背离）
            elif (recent_close[-1] < recent_close[:-1].min() and 
                  recent_macd[-1] > recent_macd[:-1].min()):
                return 1
            
            return 0
            
        except Exception as e:
            logger.error(f"检测MACD背离失败: {e}")
            return 0

class TradingSignalAnalyzer:
    """交易信号分析器"""
    
    def __init__(self):
        self.stock_old_analyzer = StockOldAnalyzer()
        self.indicators = TechnicalIndicators()
        self.fundamental_analyzer = FundamentalAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.liquidity_analyzer = LiquidityAnalyzer()
        self.market_analyzer = MarketAnalyzer()
        self.macro_analyzer = MacroAnalyzer()
        self.behavioral_analyzer = BehavioralAnalyzer()
        self.risk_analyzer = RiskAnalyzer()  # 新增风险面分析器
        self.settings = get_settings()

        # 综合评分权重配置（只用四个维度）
        self.score_weights = {
            'technical': quantRaw.score2Weight,      # 技术面权重60%
            'fundamental': quantRaw.score1Weight,    # 基本面权重0%
            'sentiment': quantRaw.score4Weight,      # 风口面权重30%
            'liquidity': quantRaw.score3Weight,      # 资金面权重10%
        }

        # 初始化优化的评分算法
        scoring_weights = ScoringWeights(
            technical=quantRaw.score2Weight,
            fundamental=quantRaw.score1Weight,
            sentiment=quantRaw.score4Weight,
            liquidity=quantRaw.score3Weight
        )
        self.optimized_scorer = OptimizedScoringAlgorithm(scoring_weights)

        # 行情参考缓存
        self.market_context = None

        # 评分算法版本标识
        self.use_optimized_scoring = True  # 是否使用优化的评分算法

        # 批量处理器
        self.batch_processor = BatchProcessor(max_workers=6)

    def analyze_market_context(self, date=None):
        """只计算一次行情参考"""
        # code参数可为None或主板指数
        self.market_context = {
            'market': self.market_analyzer.analyze_market(None),
            'risk': self.risk_analyzer.analyze_risk(None),  # 新增风险面分析
            'macro': self.macro_analyzer.analyze_macro(None),
            'behavioral': self.behavioral_analyzer.analyze_behavioral(None),
        }

    def analyze_stock(self, code: str, start_date: str = None, 
                     end_date: str = None) -> Optional[StockAnalysis]:
        """分析单只股票（只用四个维度参与评分）"""
        try:
            logger.info(f"开始分析股票: {code}")
            
            # 获取股票数据
            df = self._get_stock_data(code, start_date, end_date)
            if df is None or df.empty:
                logger.warning(f"股票 {code} 没有数据")
                return None
            
            # 获取股票名称
            stock_name = self._get_stock_name(code)

            old_analysis = self.stock_old_analyzer.analyze_stock(code, df)
            if old_analysis is None:
                logger.debug(f"股票 {code} 旧分析器返回None")
            
            # 计算技术指标
            signals = self._calculate_all_signals(df)
            
            # 生成交易信号
            trading_signals = self._generate_trading_signals(df, signals)
            
            # 计算技术面评分
            technical_score = self._calculate_technical_score(signals)
            
            # 分析基本面
            fundamental_analysis = self.fundamental_analyzer.analyze_fundamentals(code)
            fundamental_score = fundamental_analysis.overall_score if fundamental_analysis else 50.0
            
            # 分析风口面
            sentiment_analysis = self.sentiment_analyzer.analyze_sentiment(code)
            sentiment_score = sentiment_analysis.overall_score if sentiment_analysis else 50.0
            
            # 分析资金面
            liquidity_analysis = self.liquidity_analyzer.analyze_liquidity(code)
            liquidity_score = liquidity_analysis.overall_score if liquidity_analysis else 50.0
            
            # 只保留四个维度参与评分
            comprehensive_score = self._calculate_comprehensive_score(
                technical_score, fundamental_score, sentiment_score, liquidity_score
            )
            
            # 生成投资建议
            recommendation = self._generate_comprehensive_recommendation(
                trading_signals, technical_score, fundamental_score, 
                sentiment_score, liquidity_score, comprehensive_score
            )
            
            current_price = df.iloc[-1]['close']
            last_update = df.iloc[-1]['tradedate']
            
            return StockAnalysis(
                code=code,
                name=stock_name,
                current_price=current_price,
                signals=trading_signals,
                technical_score=technical_score,
                fundamental_score=fundamental_score,
                sentiment_score=sentiment_score,
                liquidity_score=liquidity_score,
                market_score=0.0,
                risk_score=0.0,
                macro_score=0.0,
                behavioral_score=0.0,
                comprehensive_score=comprehensive_score,
                risk_level=self._calculate_risk_level(df, signals),
                recommendation=recommendation,
                last_update=last_update,
                old_analysis=old_analysis
            )
            
        except Exception as e:
            logger.error(f"分析股票 {code} 失败: {e}")
            return None

    def scan_market_signals(self, date: str = None, use_optimized: bool = True) -> List[StockAnalysis]:
        """扫描市场信号，支持优化模式"""
        try:
            if not date:
                date = time.strftime('%Y%m%d', time.localtime())

            # 如果使用优化模式，使用批量处理
            if use_optimized:
                logger.info(f"使用优化模式扫描市场信号，日期: {date}")

                # 检查交易日
                if not is_trade_date(date):
                    logger.warning(f"输入日期 {date} 不是交易日")
                    return []

                # 获取股票列表
                stock_codes = fetch_all_stock(mark=False)[0:50]
                if not stock_codes:
                    logger.error("没有找到股票代码")
                    return []

                # 批量处理
                batch_results = self.batch_processor.process_stocks_batch(stock_codes, date, self)

                # 批量保存到数据库
                self._batch_save_results(batch_results)

                # 转换为 StockAnalysis 格式
                results = []
                for result in batch_results:
                    # 基于评分生成简单的交易信号
                    signals = self._generate_signals_from_scores(result)

                    analysis = type('StockAnalysis', (), {
                        'code': result['code'],
                        'name': '',
                        'current_price': result['current_price'],
                        'technical_score': result['technical_score'],
                        'fundamental_score': result['fundamental_score'],
                        'sentiment_score': result['sentiment_score'],
                        'liquidity_score': result['liquidity_score'],
                        'comprehensive_score': result['comprehensive_score'],
                        'last_update': result['last_update'],
                        'signals': signals,
                        'old_analysis': None,
                        'recommendation': self._generate_recommendation_from_score(result['comprehensive_score'])
                    })()
                    results.append(analysis)

                # 按综合评分排序
                results.sort(key=lambda x: x.comprehensive_score, reverse=True)

                logger.info(f"优化模式扫描完成，处理了 {len(results)} 只股票")
                return results

            # 原始模式
            logger.info(f"使用原始模式扫描市场信号，日期: {date}")
            # 检查输入日期是否为交易日
            if not is_trade_date(date):
                logger.warning(f"输入日期 {date} 不是交易日")
                latest_trade_date = self._get_latest_trade_date()
                if latest_trade_date:
                    logger.info(f"自动调整到最近交易日: {latest_trade_date}")
                    date = latest_trade_date
                else:
                    logger.error("无法获取最近交易日，退出扫描")
                    return []
            if not self._validate_trading_data_availability(date):
                logger.warning(f"日期 {date} 的历史数据可能不完整，继续扫描但可能影响分析质量")
            stock_codes = fetch_all_stock(mark=False)
            if not stock_codes:
                logger.error("没有找到股票代码")
                return []

            logger.info(f"获取到 {len(stock_codes)} 只股票代码，开始分析...")
            results = []
            skipped_count = 0
            data_missing_count = 0
            analysis_error_count = 0

            # 先全局计算行情参考
            self.analyze_market_context(date)
            for i, code in enumerate(stock_codes):
                try:
                    # 每处理100只股票输出一次进度
                    if i % 100 == 0:
                        logger.info(f"处理进度: {i}/{len(stock_codes)} ({i/len(stock_codes)*100:.1f}%)")

                    if not self._check_stock_data_availability(code, date):
                        data_missing_count += 1
                        logger.debug(f"股票 {code} 数据缺失")
                        continue

                    analysis = self.analyze_stock(code,
                                                start_date=ago_day_timestr(60, '%Y%m%d'),
                                                end_date=date)

                    if analysis is None:
                        analysis_error_count += 1
                        logger.debug(f"股票 {code} 分析失败，返回None")
                        continue

                    with db_manager.get_session() as session:
                        old_stock = session.query(Stock).filter(Stock.code == code).first()

                        # 存入数据库
                        if analysis:
                            new_stock = Stock(
                                code=code,
                                lastscore=getattr(old_stock, 'score', 0),
                                score=analysis.comprehensive_score,
                                score1=analysis.fundamental_score,
                                score2=analysis.technical_score,
                                score3=analysis.liquidity_score,
                                score4=analysis.sentiment_score,
                                # 兼容旧数据
                                limit=analysis.old_analysis.limit,
                                isBottomInversion=analysis.old_analysis.isBottomInversion,
                                isHeavyVolume=analysis.old_analysis.isHeavyVolume,
                                isTup=analysis.old_analysis.isTup,
                                ret=analysis.old_analysis.ret,
                                md=analysis.old_analysis.md,
                                alpha=analysis.old_analysis.alpha,
                                beta=analysis.old_analysis.beta,
                                momentum=analysis.old_analysis.momentum,
                                decisionPercent=analysis.old_analysis.decisionPercent,
                                ret10=analysis.old_analysis.ret10,
                                ret20=analysis.old_analysis.ret20,
                                ret100=analysis.old_analysis.ret100,
                                kline=analysis.old_analysis.kline,
                                emv=analysis.old_analysis.emv,
                                macd=analysis.old_analysis.macd,
                                ma=analysis.old_analysis.ma,
                                max30=analysis.old_analysis.max30,
                            )
                            session.merge(new_stock)
                            session.commit()

                    if analysis and analysis.signals:
                        results.append(analysis)
                        logger.debug(f"股票 {code} 分析成功，有信号")
                    else:
                        skipped_count += 1
                        logger.debug(f"股票 {code} 分析成功但无信号")
                except Exception as e:
                    logger.warning(f"分析股票 {code} 失败: {e}")
                    analysis_error_count += 1
                    continue
            results.sort(key=lambda x: x.technical_score, reverse=True)
            logger.info(f"市场扫描完成:")
            logger.info(f"  - 总股票数: {len(stock_codes)}")
            logger.info(f"  - 找到 {len(results)} 只有信号的股票")
            logger.info(f"  - 跳过 {skipped_count} 只股票（无信号）")
            logger.info(f"  - 分析失败 {analysis_error_count} 只股票")
            logger.info(f"  - 数据缺失 {data_missing_count} 只股票")
            logger.info(f"  - quant_stock_list 长度: {len(quant_stock_list)}")

            return results
        except Exception as e:
            logger.error(f"市场扫描失败: {e}")
            return []

    def _generate_signals_from_scores(self, result: dict) -> List[TradingSignal]:
        """基于评分生成简单的交易信号"""
        try:
            signals = []
            comprehensive_score = result['comprehensive_score']
            technical_score = result['technical_score']
            current_price = result['current_price']
            last_update = result['last_update']

            # 基于综合评分和技术评分生成信号
            if comprehensive_score >= 75 and technical_score >= 70:
                signal_type = SignalType.STRONG_BUY
                strength = min(comprehensive_score, 100)
                confidence = min(technical_score / 100.0, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，强烈买入信号"
            elif comprehensive_score >= 65 and technical_score >= 60:
                signal_type = SignalType.BUY
                strength = min(comprehensive_score * 0.8, 100)
                confidence = min(technical_score / 100.0 * 0.8, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，买入信号"
            elif comprehensive_score <= 25 and technical_score <= 30:
                signal_type = SignalType.STRONG_SELL
                strength = min(100 - comprehensive_score, 100)
                confidence = min((100 - technical_score) / 100.0, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，强烈卖出信号"
            elif comprehensive_score <= 35 and technical_score <= 40:
                signal_type = SignalType.SELL
                strength = min((100 - comprehensive_score) * 0.8, 100)
                confidence = min((100 - technical_score) / 100.0 * 0.8, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，卖出信号"
            else:
                # 不生成HOLD信号，因为过滤逻辑会跳过它们
                return []

            # 创建交易信号
            trading_signal = TradingSignal(
                date=last_update,
                signal_type=signal_type,
                confidence=confidence,
                strength=strength,
                price=current_price,
                volume=0,  # 优化模式下没有成交量数据
                indicators={},
                risk_level=self._calculate_risk_level_from_score(comprehensive_score),
                reason=reason,
                stop_loss=None,
                take_profit=None
            )
            signals.append(trading_signal)

            return signals

        except Exception as e:
            logger.error(f"基于评分生成信号失败: {e}")
            return []

    def _generate_recommendation_from_score(self, comprehensive_score: float) -> str:
        """基于综合评分生成建议"""
        if comprehensive_score >= 80:
            return "STRONG_BUY - 强烈买入"
        elif comprehensive_score >= 65:
            return "BUY - 买入"
        elif comprehensive_score >= 50:
            return "HOLD - 观望"
        elif comprehensive_score >= 35:
            return "SELL - 卖出"
        else:
            return "STRONG_SELL - 强烈卖出"

    def _calculate_risk_level_from_score(self, comprehensive_score: float) -> RiskLevel:
        """基于综合评分计算风险等级"""
        if comprehensive_score >= 75:
            return RiskLevel.LOW
        elif comprehensive_score >= 60:
            return RiskLevel.MEDIUM
        elif comprehensive_score >= 40:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH

    def _calculate_comprehensive_score(self, technical_score: float, 
                                     fundamental_score: float, 
                                     sentiment_score: float,
                                     liquidity_score: float) -> float:
        """只用四个维度加权"""
        try:
            comprehensive_score = (
                technical_score * self.score_weights['technical'] +
                fundamental_score * self.score_weights['fundamental'] +
                sentiment_score * self.score_weights['sentiment'] +
                liquidity_score * self.score_weights['liquidity']
            )
            return round(comprehensive_score, 2)
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return 50.0

    def _generate_comprehensive_recommendation(self, signals: List[TradingSignal], 
                                             technical_score: float,
                                             fundamental_score: float,
                                             sentiment_score: float,
                                             liquidity_score: float,
                                             comprehensive_score: float) -> str:
        """只用四个维度生成建议"""
        # 可根据实际业务逻辑调整
        if comprehensive_score >= 80:
            return "STRONG_BUY (强烈买入)"
        elif comprehensive_score >= 65:
            return "BUY (买入)"
        elif comprehensive_score >= 50:
            return "HOLD (观望)"
        elif comprehensive_score >= 35:
            return "SELL (卖出)"
        else:
            return "STRONG_SELL (强烈卖出)"
    
    def _get_stock_data(self, code: str, start_date: str = None,
                       end_date: str = None) -> Optional[pd.DataFrame]:
        """获取股票数据"""
        try:
            if not start_date:
                start_date = ago_day_timestr(120, '%Y%m%d')
            if not end_date:
                end_date = time.strftime('%Y%m%d', time.localtime())

            with db_manager.get_session() as session:
                # 使用安全字段列表，避免查询不存在的字段
                # 基于模型分析结果，只使用确认存在的字段
                safe_query = session.query(
                    StockHistory.id,
                    StockHistory.code,
                    StockHistory.date,
                    StockHistory.tradedate,
                    StockHistory.open,
                    StockHistory.high,
                    StockHistory.low,
                    StockHistory.close,
                    StockHistory.volume,
                    StockHistory.amount,
                    StockHistory.turn,
                    StockHistory.pctChg
                ).filter(
                    and_(
                        StockHistory.code == code,
                        StockHistory.tradedate >= start_date,
                        StockHistory.tradedate <= end_date
                    )
                ).order_by(StockHistory.tradedate.asc())

                # 尝试添加扩展字段（如果存在的话）
                try:
                    # 先测试是否可以查询扩展字段
                    extended_query = session.query(
                        StockHistory.id,
                        StockHistory.code,
                        StockHistory.date,
                        StockHistory.tradedate,
                        StockHistory.open,
                        StockHistory.high,
                        StockHistory.low,
                        StockHistory.close,
                        StockHistory.volume,
                        StockHistory.amount,
                        StockHistory.turn,
                        StockHistory.pctChg,
                        StockHistory.ret30,
                        StockHistory.max,
                        StockHistory.min,
                        StockHistory.rose3,
                        StockHistory.fall3,
                        StockHistory.peTTM,
                        StockHistory.psTTM,
                        StockHistory.pcfNcfTTM,
                        StockHistory.pbMRQ
                    ).filter(
                        and_(
                            StockHistory.code == code,
                            StockHistory.tradedate >= start_date,
                            StockHistory.tradedate <= end_date
                        )
                    ).order_by(StockHistory.tradedate.asc()).limit(1)

                    # 测试查询是否成功
                    test_result = extended_query.first()
                    if test_result is not None:
                        # 扩展字段存在，使用完整查询
                        query = extended_query.limit(None)
                    else:
                        # 使用安全查询
                        query = safe_query

                except Exception as e:
                    logger.warning(f"扩展字段查询失败，使用安全字段: {e}")
                    query = safe_query

                df = pd.read_sql(query.statement, session.bind)

                if df.empty:
                    logger.warning(f"股票 {code} 在时间段 {start_date}-{end_date} 没有数据")
                    return None
                return df

        except Exception as e:
            logger.error(f"获取股票 {code} 数据失败: {e}")
            return None
    
    def _get_stock_name(self, code: str) -> str:
        """获取股票名称"""
        try:
            with db_manager.get_session() as session:
                stock = session.query(StockCode).filter(StockCode.code == code).first()
                return stock.name if stock else code
        except Exception as e:
            logger.error(f"获取股票 {code} 名称失败: {e}")
            return code
    
    def _get_latest_trade_date(self) -> Optional[str]:
        """获取小于今天的最近交易日"""
        try:
            # 获取今天的日期
            today = datetime.now().strftime('%Y%m%d')
            
            with db_manager.get_session() as session:
                result = (session.query(Daliy.date)
                         .filter(and_(
                             Daliy.isOpen == True,
                             Daliy.date <= today
                         ))
                         .order_by(desc(Daliy.date))
                         .first())
                return result.date if result else None
        except Exception as e:
            logger.error(f"获取最新交易日失败: {e}")
            return None
    
    def _validate_trading_data_availability(self, target_date: str) -> bool:
        """验证目标日期的交易数据可用性"""
        try:
            # 检查目标日期是否有足够的股票数据
            with db_manager.get_session() as session:
                # 统计目标日期的股票数据数量
                stock_count = (session.query(StockHistory)
                              .filter(StockHistory.tradedate == target_date)
                              .count())
                
                # 获取总股票数量作为参考
                total_stocks = session.query(Stock).count()
                
                # 如果数据覆盖率低于50%，认为数据不完整
                coverage_ratio = stock_count / total_stocks if total_stocks > 0 else 0
                
                logger.info(f"日期 {target_date} 数据覆盖率: {coverage_ratio:.1%} ({stock_count}/{total_stocks})")
                
                return coverage_ratio >= 0.5
                
        except Exception as e:
            logger.error(f"验证交易数据可用性失败: {e}")
            return False
    
    def _check_stock_data_availability(self, code: str, target_date: str) -> bool:
        """检查单只股票的数据可用性"""
        try:
            # 检查目标日期是否有该股票的数据
            with db_manager.get_session() as session:
                # 明确指定查询字段，避免查询不存在的字段
                stock_data = (session.query(
                                 StockHistory.id,
                                 StockHistory.code,
                                 StockHistory.tradedate
                             )
                             .filter(and_(
                                 StockHistory.code == code,
                                 StockHistory.tradedate == target_date
                             ))
                             .first())

                if not stock_data:
                    return False

                # 检查是否有足够的历史数据（至少60个交易日）
                start_date = ago_day_timestr(120, '%Y%m%d')  # 获取更多历史数据
                historical_data = (session.query(StockHistory.id)
                                 .filter(and_(
                                     StockHistory.code == code,
                                     StockHistory.tradedate >= start_date,
                                     StockHistory.tradedate <= target_date
                                 ))
                                 .count())

                # 至少需要60个交易日的数据
                return historical_data >= 60

        except Exception as e:
            logger.error(f"检查股票 {code} 数据可用性失败: {e}")
            return False
    
    def _calculate_all_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算所有技术指标信号"""
        signals = {}
        
        # 计算各类技术指标
        signals.update(self.indicators.calculate_ma_signals(df))
        signals.update(self.indicators.calculate_macd_signals(df))
        signals.update(self.indicators.calculate_rsi_signals(df))
        signals.update(self.indicators.calculate_volume_indicators(df))  # 更新为新的成交量指标
        signals.update(self.indicators.calculate_kdj_signals(df))
        signals.update(self.indicators.calculate_support_resistance(df))
        
        # 新增技术指标
        signals.update(self.indicators.calculate_bollinger_bands(df))
        signals.update(self.indicators.calculate_atr_signals(df))
        signals.update(self.indicators.calculate_cci_signals(df))
        signals.update(self.indicators.calculate_williams_r(df))
        signals.update(self.indicators.calculate_stochastic_signals(df))
        signals.update(self.indicators.calculate_adx_signals(df))
        signals.update(self.indicators.calculate_momentum_indicators(df))
        
        return signals

    def calculate_optimized_stock_score(self, code: str, df: pd.DataFrame) -> Dict[str, Any]:
        """
        使用优化算法计算股票评分
        保持权重不变，但改进评分逻辑
        """
        try:
            # 计算技术指标信号
            signals = self._calculate_all_signals(df)

            # 获取各维度数据
            fundamental_data = self.fundamental_analyzer._get_fundamental_data(code)
            sentiment_data = self.sentiment_analyzer._get_sentiment_data(code)
            liquidity_data = self.liquidity_analyzer._get_liquidity_data(code)

            # 使用优化算法计算各维度评分
            technical_score = self.optimized_scorer.calculate_technical_score(signals)
            fundamental_score = 50.0  # 基本面权重为0，固定50分
            sentiment_score = self.optimized_scorer.calculate_sentiment_score(sentiment_data)
            liquidity_score = self.optimized_scorer.calculate_liquidity_score(liquidity_data)

            # 计算综合评分
            comprehensive_score = self.optimized_scorer.calculate_comprehensive_score(
                technical_score, fundamental_score, sentiment_score, liquidity_score
            )

            # 获取详细评分分解
            score_breakdown = self.optimized_scorer.get_score_breakdown(
                technical_score, fundamental_score, sentiment_score, liquidity_score
            )

            logger.debug(f"股票 {code} 优化评分: 技术面={technical_score:.1f}, "
                        f"风口面={sentiment_score:.1f}, 资金面={liquidity_score:.1f}, "
                        f"综合={comprehensive_score:.1f}")

            return {
                'code': code,
                'technical_score': technical_score,
                'fundamental_score': fundamental_score,
                'sentiment_score': sentiment_score,
                'liquidity_score': liquidity_score,
                'comprehensive_score': comprehensive_score,
                'score_breakdown': score_breakdown,
                'algorithm_version': 'optimized_v1.0'
            }

        except Exception as e:
            logger.error(f"计算股票 {code} 优化评分失败: {e}")
            return {
                'code': code,
                'technical_score': 50.0,
                'fundamental_score': 50.0,
                'sentiment_score': 50.0,
                'liquidity_score': 50.0,
                'comprehensive_score': 50.0,
                'error': str(e)
            }

    def _generate_trading_signals(self, df: pd.DataFrame,
                                signals: Dict[str, Any]) -> List[TradingSignal]:
        """生成交易信号（只输出极强信号）"""
        trading_signals = []
        try:
            current = df.iloc[-1]
            current_price = current['close']
            current_volume = current['volume']
            current_date = current['tradedate']
            # 综合信号强度计算
            signal_strength = 0
            strength_details = {}
            reasons = []
            # 均线信号
            if signals.get('ma_bullish'):
                ma_contribution = 20
                signal_strength += ma_contribution
                strength_details['ma_bullish'] = ma_contribution
                reasons.append("均线多头排列")
            elif signals.get('ma_bullish') is False:
                ma_contribution = -20
                signal_strength += ma_contribution
                strength_details['ma_bearish'] = ma_contribution
                reasons.append("均线空头排列")
            
            if signals.get('golden_cross'):
                cross_contribution = 15
                signal_strength += cross_contribution
                strength_details['golden_cross'] = cross_contribution
                reasons.append("均线金叉")
            elif signals.get('death_cross'):
                cross_contribution = -15
                signal_strength += cross_contribution
                strength_details['death_cross'] = cross_contribution
                reasons.append("均线死叉")
            
            # MACD信号
            if signals.get('macd_golden_cross'):
                macd_contribution = 15
                signal_strength += macd_contribution
                strength_details['macd_golden_cross'] = macd_contribution
                reasons.append("MACD金叉")
            elif signals.get('macd_death_cross'):
                macd_contribution = -15
                signal_strength += macd_contribution
                strength_details['macd_death_cross'] = macd_contribution
                reasons.append("MACD死叉")
            
            if signals.get('macd_divergence') == 1:
                div_contribution = 10
                signal_strength += div_contribution
                strength_details['macd_bullish_divergence'] = div_contribution
                reasons.append("MACD底背离")
            elif signals.get('macd_divergence') == -1:
                div_contribution = -10
                signal_strength += div_contribution
                strength_details['macd_bearish_divergence'] = div_contribution
                reasons.append("MACD顶背离")
            
            # RSI信号
            if signals.get('rsi_oversold'):
                rsi_contribution = 10
                signal_strength += rsi_contribution
                strength_details['rsi_oversold'] = rsi_contribution
                reasons.append("RSI超卖")
            elif signals.get('rsi_overbought'):
                rsi_contribution = -10
                signal_strength += rsi_contribution
                strength_details['rsi_overbought'] = rsi_contribution
                reasons.append("RSI超买")
            
            # KDJ信号
            if signals.get('kdj_golden_cross'):
                kdj_contribution = 10
                signal_strength += kdj_contribution
                strength_details['kdj_golden_cross'] = kdj_contribution
                reasons.append("KDJ金叉")
            elif signals.get('kdj_death_cross'):
                kdj_contribution = -10
                signal_strength += kdj_contribution
                strength_details['kdj_death_cross'] = kdj_contribution
                reasons.append("KDJ死叉")
            
            # 成交量信号
            if signals.get('volume_price_positive'):
                vol_contribution = 10
                signal_strength += vol_contribution
                strength_details['volume_price_positive'] = vol_contribution
                reasons.append("放量上涨")
            elif signals.get('volume_price_negative'):
                vol_contribution = -10
                signal_strength += vol_contribution
                strength_details['volume_price_negative'] = vol_contribution
                reasons.append("放量下跌")
            
            # 支撑阻力信号
            if signals.get('near_support'):
                support_contribution = 5
                signal_strength += support_contribution
                strength_details['near_support'] = support_contribution
                reasons.append("接近支撑位")
            elif signals.get('near_resistance'):
                resistance_contribution = -5
                signal_strength += resistance_contribution
                strength_details['near_resistance'] = resistance_contribution
                reasons.append("接近阻力位")
            
            # 布林带信号
            if signals.get('bb_lower_breakout'):
                bb_contribution = 8
                signal_strength += bb_contribution
                strength_details['bb_lower_breakout'] = bb_contribution
                reasons.append("布林带下轨突破")
            elif signals.get('bb_upper_breakout'):
                bb_contribution = -8
                signal_strength += bb_contribution
                strength_details['bb_upper_breakout'] = bb_contribution
                reasons.append("布林带上轨突破")
            
            if signals.get('bb_squeeze'):
                bb_squeeze_contribution = 3
                signal_strength += bb_squeeze_contribution
                strength_details['bb_squeeze'] = bb_squeeze_contribution
                reasons.append("布林带收缩")
            
            # ATR信号
            if signals.get('atr_low_volatility'):
                atr_contribution = 3
                signal_strength += atr_contribution
                strength_details['atr_low_volatility'] = atr_contribution
                reasons.append("低波动率")
            elif signals.get('atr_high_volatility'):
                atr_contribution = -3
                signal_strength += atr_contribution
                strength_details['atr_high_volatility'] = atr_contribution
                reasons.append("高波动率")
            
            # CCI信号
            if signals.get('cci_oversold'):
                cci_contribution = 8
                signal_strength += cci_contribution
                strength_details['cci_oversold'] = cci_contribution
                reasons.append("CCI超卖")
            elif signals.get('cci_overbought'):
                cci_contribution = -8
                signal_strength += cci_contribution
                strength_details['cci_overbought'] = cci_contribution
                reasons.append("CCI超买")
            
            if signals.get('cci_bullish_divergence'):
                cci_div_contribution = 10
                signal_strength += cci_div_contribution
                strength_details['cci_bullish_divergence'] = cci_div_contribution
                reasons.append("CCI底背离")
            elif signals.get('cci_bearish_divergence'):
                cci_div_contribution = -10
                signal_strength += cci_div_contribution
                strength_details['cci_bearish_divergence'] = cci_div_contribution
                reasons.append("CCI顶背离")
            
            # 威廉指标信号
            if signals.get('williams_oversold'):
                williams_contribution = 8
                signal_strength += williams_contribution
                strength_details['williams_oversold'] = williams_contribution
                reasons.append("威廉指标超卖")
            elif signals.get('williams_overbought'):
                williams_contribution = -8
                signal_strength += williams_contribution
                strength_details['williams_overbought'] = williams_contribution
                reasons.append("威廉指标超买")
            
            # 随机指标信号
            if signals.get('stoch_oversold'):
                stoch_contribution = 8
                signal_strength += stoch_contribution
                strength_details['stoch_oversold'] = stoch_contribution
                reasons.append("随机指标超卖")
            elif signals.get('stoch_overbought'):
                stoch_contribution = -8
                signal_strength += stoch_contribution
                strength_details['stoch_overbought'] = stoch_contribution
                reasons.append("随机指标超买")
            
            if signals.get('stoch_golden_cross'):
                stoch_cross_contribution = 8
                signal_strength += stoch_cross_contribution
                strength_details['stoch_golden_cross'] = stoch_cross_contribution
                reasons.append("随机指标金叉")
            elif signals.get('stoch_death_cross'):
                stoch_cross_contribution = -8
                signal_strength += stoch_cross_contribution
                strength_details['stoch_death_cross'] = stoch_cross_contribution
                reasons.append("随机指标死叉")
            
            # ADX信号
            if signals.get('adx_strong_trend') and signals.get('adx_bullish'):
                adx_contribution = 10
                signal_strength += adx_contribution
                strength_details['adx_strong_bullish'] = adx_contribution
                reasons.append("ADX强势多头")
            elif signals.get('adx_strong_trend') and signals.get('adx_bearish'):
                adx_contribution = -10
                signal_strength += adx_contribution
                strength_details['adx_strong_bearish'] = adx_contribution
                reasons.append("ADX强势空头")
            
            # 动量信号
            if signals.get('price_near_low'):
                momentum_contribution = 5
                signal_strength += momentum_contribution
                strength_details['price_near_low'] = momentum_contribution
                reasons.append("价格接近低点")
            elif signals.get('price_near_high'):
                momentum_contribution = -5
                signal_strength += momentum_contribution
                strength_details['price_near_high'] = momentum_contribution
                reasons.append("价格接近高点")
            
            # 成交量趋势信号
            if signals.get('volume_trend_increasing'):
                vol_trend_contribution = 3
                signal_strength += vol_trend_contribution
                strength_details['volume_trend_increasing'] = vol_trend_contribution
                reasons.append("成交量趋势上升")
            elif signals.get('volume_trend_decreasing'):
                vol_trend_contribution = -3
                signal_strength += vol_trend_contribution
                strength_details['volume_trend_decreasing'] = vol_trend_contribution
                reasons.append("成交量趋势下降")
            
            # 计算信号强度（0-100）
            # 将信号强度标准化到0-100范围
            normalized_strength = min(max(abs(signal_strength), 0), 100)
            
            # 确定信号类型
            if signal_strength >= 30:
                signal_type = SignalType.STRONG_BUY
            elif signal_strength >= 15:
                signal_type = SignalType.BUY
            elif signal_strength <= -30:
                signal_type = SignalType.STRONG_SELL
            elif signal_strength <= -15:
                signal_type = SignalType.SELL
            else:
                signal_type = SignalType.HOLD
            
            # 计算置信度
            confidence = min(abs(signal_strength) / 50.0, 1.0)
            
            # 计算风险等级
            risk_level = self._calculate_risk_level(df, signals)
            
            # 计算止损止盈
            stop_loss, take_profit = self._calculate_stop_loss_take_profit(
                current_price, signals, signal_type
            )
            
            # 创建交易信号
            if signal_type != SignalType.HOLD:
                trading_signal = TradingSignal(
                    date=current_date,
                    signal_type=signal_type,
                    confidence=confidence,
                    strength=normalized_strength,
                    price=current_price,
                    volume=current_volume,
                    indicators=signals,
                    risk_level=risk_level,
                    reason=", ".join(reasons),
                    stop_loss=stop_loss,
                    take_profit=take_profit
                )
                trading_signals.append(trading_signal)
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
        
        return trading_signals
    
    def _calculate_technical_score(self, signals: Dict[str, Any]) -> float:
        """计算技术面评分"""
        try:
            score = 50.0  # 基础分
            
            # 趋势评分
            if signals.get('ma_bullish'):
                score += 20
            elif signals.get('ma_bullish') is False:
                score -= 20
            
            # 动量评分
            if signals.get('macd_signal') == 1:
                score += 15
            elif signals.get('macd_signal') == -1:
                score -= 15
            
            if signals.get('rsi_signal') == 1:
                score += 10
            elif signals.get('rsi_signal') == -1:
                score -= 10
            
            # 成交量评分
            if signals.get('volume_price_positive'):
                score += 10
            elif signals.get('volume_price_negative'):
                score -= 10
            
            # 确保分数在0-100范围内
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算技术评分失败: {e}")
            return 50.0

    def _batch_save_results(self, results: list):
        """批量保存结果到数据库"""
        try:
            if not results:
                return

            logger.info(f"开始批量保存 {len(results)} 条记录到数据库")

            # 准备批量插入数据
            stock_records = []
            for result in results:
                stock_record = {
                    'code': result['code'],
                    'score': result['comprehensive_score'],
                    'score1': result['fundamental_score'],
                    'score2': result['technical_score'],
                    'score3': result['liquidity_score'],
                    'score4': result['sentiment_score'],
                    'lastscore': 0,
                    # 其他字段使用默认值
                    'limit': 0,
                    'isBottomInversion': False,
                    'isHeavyVolume': False,
                    'isTup': False,
                    'ret': 0.0,
                    'md': 0.0,
                    'alpha': 0.0,
                    'beta': 0.0,
                    'momentum': False,
                    'decisionPercent': 0.0,
                    'ret10': 0.0,
                    'ret20': 0.0,
                    'ret100': 0.0,
                    'kline': '',
                    'emv': False,
                    'macd': 0.0,
                    'ma': False,
                    'max30': False,
                }
                stock_records.append(stock_record)

            # 批量插入/更新
            with db_manager.get_session() as session:
                for record in stock_records:
                    insert_stmt = insert(Stock).values(**record)
                    on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**record)
                    session.execute(on_duplicate_key_stmt)

                session.commit()

            logger.info("批量保存完成")

        except Exception as e:
            logger.error(f"批量保存到数据库失败: {e}")
    
    def _calculate_risk_score(self, df: pd.DataFrame, signals: Dict[str, Any]) -> float:
        """计算风险评分"""
        try:
            risk_score = 50.0  # 基础风险分
            
            # 波动率风险
            returns = df['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            
            if volatility > 0.5:  # 高波动
                risk_score += 30
            elif volatility > 0.3:  # 中等波动
                risk_score += 15
            
            # 价格位置风险
            if signals.get('rsi_overbought'):
                risk_score += 20
            elif signals.get('rsi_oversold'):
                risk_score -= 10
            
            # 成交量风险
            if signals.get('volume_surge') and signals.get('volume_price_negative'):
                risk_score += 15
            
            # 确保分数在0-100范围内
            return max(0, min(100, risk_score))
            
        except Exception as e:
            logger.error(f"计算风险评分失败: {e}")
            return 50.0
    
    def _calculate_risk_level(self, df: pd.DataFrame, signals: Dict[str, Any]) -> RiskLevel:
        """计算风险等级"""
        try:
            risk_score = self._calculate_risk_score(df, signals)
            
            if risk_score >= 80:
                return RiskLevel.VERY_HIGH
            elif risk_score >= 60:
                return RiskLevel.HIGH
            elif risk_score >= 40:
                return RiskLevel.MEDIUM
            else:
                return RiskLevel.LOW
                
        except Exception as e:
            logger.error(f"计算风险等级失败: {e}")
            return RiskLevel.MEDIUM
    
    def _calculate_stop_loss_take_profit(self, current_price: float, 
                                       signals: Dict[str, Any], 
                                       signal_type: SignalType) -> Tuple[Optional[float], Optional[float]]:
        """计算止损止盈位"""
        try:
            if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                # 买入信号：止损位在支撑位下方，止盈位在阻力位
                stop_loss = signals.get('nearest_support')
                if stop_loss:
                    stop_loss = stop_loss * 0.95  # 支撑位下方5%
                
                take_profit = signals.get('nearest_resistance')
                if take_profit:
                    take_profit = take_profit * 1.05  # 阻力位上方5%
                
                return stop_loss, take_profit
                
            elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                # 卖出信号：止损位在阻力位上方，止盈位在支撑位
                stop_loss = signals.get('nearest_resistance')
                if stop_loss:
                    stop_loss = stop_loss * 1.05  # 阻力位上方5%
                
                take_profit = signals.get('nearest_support')
                if take_profit:
                    take_profit = take_profit * 0.95  # 支撑位下方5%
                
                return stop_loss, take_profit
            
            return None, None
            
        except Exception as e:
            logger.error(f"计算止损止盈失败: {e}")
            return None, None
    
    def analyze_signal_strength(self, signal: TradingSignal) -> Dict[str, Any]:
        """分析信号强度构成"""
        try:
            strength_analysis = {
                'total_strength': signal.strength,
                'confidence': signal.confidence,
                'signal_type': signal.signal_type.value,
                'risk_level': signal.risk_level.value,
                'contributions': {}
            }
            
            # 分析各指标对信号强度的贡献
            indicators = signal.indicators
            
            # 均线贡献
            if indicators.get('ma_bullish'):
                strength_analysis['contributions']['均线多头排列'] = 20
            elif indicators.get('ma_bullish') is False:
                strength_analysis['contributions']['均线空头排列'] = -20
            
            if indicators.get('golden_cross'):
                strength_analysis['contributions']['均线金叉'] = 15
            elif indicators.get('death_cross'):
                strength_analysis['contributions']['均线死叉'] = -15
            
            # MACD贡献
            if indicators.get('macd_golden_cross'):
                strength_analysis['contributions']['MACD金叉'] = 15
            elif indicators.get('macd_death_cross'):
                strength_analysis['contributions']['MACD死叉'] = -15
            
            if indicators.get('macd_divergence') == 1:
                strength_analysis['contributions']['MACD底背离'] = 10
            elif indicators.get('macd_divergence') == -1:
                strength_analysis['contributions']['MACD顶背离'] = -10
            
            # RSI贡献
            if indicators.get('rsi_oversold'):
                strength_analysis['contributions']['RSI超卖'] = 10
            elif indicators.get('rsi_overbought'):
                strength_analysis['contributions']['RSI超买'] = -10
            
            # KDJ贡献
            if indicators.get('kdj_golden_cross'):
                strength_analysis['contributions']['KDJ金叉'] = 10
            elif indicators.get('kdj_death_cross'):
                strength_analysis['contributions']['KDJ死叉'] = -10
            
            # 成交量贡献
            if indicators.get('volume_price_positive'):
                strength_analysis['contributions']['放量上涨'] = 10
            elif indicators.get('volume_price_negative'):
                strength_analysis['contributions']['放量下跌'] = -10
            
            # 支撑阻力贡献
            if indicators.get('near_support'):
                strength_analysis['contributions']['接近支撑位'] = 5
            elif indicators.get('near_resistance'):
                strength_analysis['contributions']['接近阻力位'] = -5
            
            # 布林带贡献
            if indicators.get('bb_lower_breakout'):
                strength_analysis['contributions']['布林带下轨突破'] = 8
            elif indicators.get('bb_upper_breakout'):
                strength_analysis['contributions']['布林带上轨突破'] = -8
            
            if indicators.get('bb_squeeze'):
                strength_analysis['contributions']['布林带收缩'] = 3
            
            # CCI贡献
            if indicators.get('cci_oversold'):
                strength_analysis['contributions']['CCI超卖'] = 8
            elif indicators.get('cci_overbought'):
                strength_analysis['contributions']['CCI超买'] = -8
            
            if indicators.get('cci_bullish_divergence'):
                strength_analysis['contributions']['CCI底背离'] = 10
            elif indicators.get('cci_bearish_divergence'):
                strength_analysis['contributions']['CCI顶背离'] = -10
            
            # 威廉指标贡献
            if indicators.get('williams_oversold'):
                strength_analysis['contributions']['威廉指标超卖'] = 8
            elif indicators.get('williams_overbought'):
                strength_analysis['contributions']['威廉指标超买'] = -8
            
            # 随机指标贡献
            if indicators.get('stoch_oversold'):
                strength_analysis['contributions']['随机指标超卖'] = 8
            elif indicators.get('stoch_overbought'):
                strength_analysis['contributions']['随机指标超买'] = -8
            
            if indicators.get('stoch_golden_cross'):
                strength_analysis['contributions']['随机指标金叉'] = 8
            elif indicators.get('stoch_death_cross'):
                strength_analysis['contributions']['随机指标死叉'] = -8
            
            # ADX贡献
            if indicators.get('adx_strong_trend') and indicators.get('adx_bullish'):
                strength_analysis['contributions']['ADX强势多头'] = 10
            elif indicators.get('adx_strong_trend') and indicators.get('adx_bearish'):
                strength_analysis['contributions']['ADX强势空头'] = -10
            
            # 动量贡献
            if indicators.get('price_near_low'):
                strength_analysis['contributions']['价格接近低点'] = 5
            elif indicators.get('price_near_high'):
                strength_analysis['contributions']['价格接近高点'] = -5
            
            # 成交量趋势贡献
            if indicators.get('volume_trend_increasing'):
                strength_analysis['contributions']['成交量趋势上升'] = 3
            elif indicators.get('volume_trend_decreasing'):
                strength_analysis['contributions']['成交量趋势下降'] = -3
            
            return strength_analysis
            
        except Exception as e:
            logger.error(f"分析信号强度失败: {e}")
            return {}

def print_analysis_result(analysis: StockAnalysis, market_context=None):
    """打印分析结果，支持行情参考"""
    print(f"\n{'='*80}")
    print(f"📊 股票分析报告: {analysis.code} ({analysis.name})")
    print(f"{'='*80}")
    print(f"📅 分析日期: {analysis.last_update}")
    print(f"💰 当前价格: ¥{analysis.current_price:.2f}")
    print(f"📈 技术面评分: {analysis.technical_score:.1f}/100")
    print(f"📊 基本面评分: {analysis.fundamental_score:.1f}/100")
    print(f"📰 风口面评分: {analysis.sentiment_score:.1f}/100")
    print(f"💰 资金面评分: {analysis.liquidity_score:.1f}/100")
    print(f"🎯 综合评分: {analysis.comprehensive_score:.1f}/100")
    print(f"💡 投资建议: {analysis.recommendation}")
    if analysis.signals:
        print(f"\n🎯 交易信号:")
        for i, signal in enumerate(analysis.signals, 1):
            print(f"  {i}. {signal.signal_type.value} (置信度: {signal.confidence:.1%}, 强度: {signal.strength:.1f}/100)")
            print(f"     价格: ¥{signal.price:.2f} | 成交量: {signal.volume:,.0f}")
            print(f"     原因: {signal.reason}")
            if signal.stop_loss:
                print(f"     止损: ¥{signal.stop_loss:.2f}")
            if signal.take_profit:
                print(f"     止盈: ¥{signal.take_profit:.2f}")
            print(f"     风险等级: {signal.risk_level.value}")
            # 显示技术指标详情（略）
    else:
        print(f"\n📊 暂无明确交易信号")
    if market_context:
        print("----------------------------------------")
        print("【行情参考】")
        if market_context.get('macro'):
            print(f"宏观面评分: {market_context['macro'].overall_score:.2f}/100")
        if market_context.get('risk'):
            print(f"风险面评分: {market_context['risk'].overall_score:.2f}/100")
        if market_context.get('behavioral'):
            print(f"行为面评分: {market_context['behavioral'].overall_score:.2f}/100")
        if market_context.get('market'):
            print(f"市场面评分: {market_context['market'].overall_score:.2f}/100")
    print(f"{'='*80}")

def print_market_scan_results(results: List[StockAnalysis], market_context=None, limit: int = 20):
    """打印市场扫描结果，支持行情参考"""
    # 只保留最强信号的股票
    filtered_results = []
    for analysis in results:
        if not analysis.signals:
            continue
        last_signal = analysis.signals[-1]
        # 存入数据库
        with db_manager.get_session() as session:
            new_stock = Stock(
                code=analysis.code,
                desc2=last_signal.reason,
            )
            session.merge(new_stock)
            session.commit()
        if (
            last_signal.signal_type in [SignalType.STRONG_BUY, SignalType.STRONG_SELL]
            and last_signal.strength >= 50
            and last_signal.confidence >= 0.65
            and analysis.comprehensive_score >= 60
        ):
            filtered_results.append(analysis)
    print(f"\n{'='*140}")
    print(f"🔍 市场信号扫描结果 (显示前{limit}只)")
    print(f"{'='*160}")
    print(f"{'代码':<8} {'名称':<12} {'价格':<8} {'技术':<6} {'基本':<6} {'风口':<6} {'资金':<6} {'综合':<6} {'信号强度':<8} {'信号类型':<12} {'原因'} {'建议'}")
    print(f"{'-'*160}")
    for i, analysis in enumerate(filtered_results[:limit], 1):
        signal_type = analysis.signals[-1].signal_type.value if analysis.signals else "HOLD"
        signal_strength = analysis.signals[-1].strength if analysis.signals else 0
        recommendation = analysis.recommendation.split(' - ')[0] if analysis.recommendation else "观望"
        reason = analysis.signals[-1].reason if analysis.signals else ""
        print(f"{analysis.code:<8} {analysis.name:<12} "
              f"¥{analysis.current_price:<7.2f} {analysis.technical_score:<6.1f} "
              f"{analysis.fundamental_score:<6.1f} {analysis.sentiment_score:<6.1f} "
              f"{analysis.liquidity_score:<6.1f} {analysis.comprehensive_score:<6.1f} "
              f"{signal_strength:<8.1f} {signal_type:<12} {reason} {recommendation}")
    if market_context:
        print("----------------------------------------")
        print("【行情参考】")
        if market_context.get('macro'):
            print(f"宏观面评分: {market_context['macro'].overall_score:.2f}/100")
        if market_context.get('risk'):
            print(f"风险面评分: {market_context['risk'].overall_score:.2f}/100")
        if market_context.get('behavioral'):
            print(f"行为面评分: {market_context['behavioral'].overall_score:.2f}/100")
        if market_context.get('market'):
            print(f"市场面评分: {market_context['market'].overall_score:.2f}/100")
    print(f"{'='*160}")


# 其它功能补充
def extra_function():
    """
    其它功能补充
    """
    logger.info(f"完成股票分析，共处理 {len(quant_stock_list)} 只股票")

    if not quant_stock_list:
        logger.warning("没有分析结果，跳过策略筛选")
        return False

    # 验证数据完整性
    logger.info(f"收集到 {len(quant_stock_list)} 个分析结果")

    # 验证数据结构
    valid_results = []
    for i, result in enumerate(quant_stock_list):
        if result is None:
            logger.warning(f"跳过第 {i} 个空结果")
            continue
        if not isinstance(result, list) or len(result) != 11:
            logger.warning(f"跳过第 {i} 个格式错误的结果: {type(result)}, 长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")
            continue
        valid_results.append(result)

    if not valid_results:
        logger.error("没有找到格式正确的分析结果")
        return False

    logger.info(f"验证通过，有效结果数: {len(valid_results)}")

    # 量化选股结果整理
    df_quant = pd.DataFrame(
        data=valid_results,
        columns=['code', 'isBottomInversion', 'isHeavyVolume', 'isTup', 'macd', 'ma', 'emv',
                'strongTrend', 'breakoutPlatform', 'bigDivergence', 'fundAccumulation']
    )

    # 各种策略筛选和保存
    strategies = [
        # 优化后的原有策略
        ('[量化]底部反转', '持续下跌后的放量大阳，均线上穿', 'isBottomInversion', True),
        ('[量化]成交量是前5天的两倍', '放量实体阳线', 'isHeavyVolume', True),
        ('[量化]三连阳', '连续三日上涨模式', 'isTup', True),
        ('[量化]底背离', 'MACD底背离信号', 'macd', -1),
        ('[量化]5日线上穿10日线和30日线', '均线多头排列', 'ma', True),
        ('[量化]EMV信号', 'EMV技术指标信号', 'emv', True),
        # 新增策略
        ('[量化]强势趋势股', '沿着5日线上涨，强势特征明显', 'strongTrend', True),
        ('[量化]脱离底部平台', '成交量小碎步放大，行情刚起步', 'breakoutPlatform', True),
        ('[量化]大分歧', '两天长上下影，市场分歧转折点', 'bigDivergence', True),
        ('[量化]资金吸筹', '主力资金持续吸筹，量价配合良好', 'fundAccumulation', True)
    ]

    for name, desc, column, value in strategies:
        try:
            if column in df_quant.columns:
                selected_stocks = df_quant[df_quant[column] == value]['code'].tolist()
                save_sql(name, desc, selected_stocks)
            else:
                logger.warning(f"列 {column} 不存在，跳过策略 {name}")
        except Exception as e:
            logger.error(f"处理策略 {name} 失败: {e}")

    logger.info("功能1完成: 个股量化分析和策略筛选")


    """
    功能2: 标星个股计算和趋势股票筛选

    :return: 是否成功
    """
    try:
        logger.info("功能2: 标星个股计算")

        today = time.strftime('%Y%m%d', time.localtime(time.time()))

        with db_manager.get_session() as session:
            # 清除之前的标星状态
            session.query(Stock).filter(Stock.quant == 1).update({
                "quant": False,
                "star": False
            })
            session.commit()

            # 构建查询SQL
            sql = '''
                SELECT a.code, a.decisionPercent, a.kline, a.momentum,
                       b.f20, b.f6, b.displayName
                FROM stock a
                LEFT JOIN source b ON b.code = a.code
                WHERE b.f20 > {value} AND b.f6 > {amount}
                  AND a.momentum = true
                ORDER BY a.decisionPercent DESC
                LIMIT 1000
            '''.format(value=quantRaw.averageValue, amount=quantRaw.averageAmount)

            # 执行查询
            result = session.execute(sql)
            rows = result.fetchall()

            if not rows:
                logger.warning("没有找到符合条件的标星股票")
                return False

            logger.info(f"找到 {len(rows)} 只符合标星条件的股票")

            # 更新每日统计
            new_daily = Daliy(
                date=today,
                starcount=len(rows)
            )
            session.merge(new_daily)

            # 批量更新标星状态
            star_codes = []
            for row in rows:
                star_codes.append(row['code'])

                item = {
                    'code': row['code'],
                    'star': True,
                    'quant': True
                }
                insert_stmt = insert(Stock).values(**item)
                on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
                session.execute(on_duplicate_key_stmt)

            session.commit()

            logger.info(f"功能2完成: 标星 {len(star_codes)} 只股票")
            return True

    except Exception as e:
        logger.error(f"功能2执行失败: {e}")
        return False

@monitor_performance
def main():
    """主函数"""
    try:
        logger.info("🚀 启动股票买卖点分析工具")
        
        # 解析命令行参数
        parser = argparse.ArgumentParser(description="股票买卖点分析工具")
        parser.add_argument("-c", "--code", type=str, help="股票代码")
        parser.add_argument("-b", "--begin", type=str, help="开始日期 (YYYYMMDD)")
        parser.add_argument("-e", "--end", type=str, help="结束日期 (YYYYMMDD)")
        parser.add_argument("-d", "--date", type=str, help="分析日期 (YYYYMMDD)")
        parser.add_argument("--scan", action="store_true", help="扫描市场信号")
        parser.add_argument("--limit", type=int, default=20, help="显示结果数量限制")
        parser.add_argument("--optimized", action="store_true", help="使用优化模式（更快的计算）")
        parser.add_argument("--workers", type=int, default=8, help="并行处理的线程数")
        
        args = parser.parse_args()
        
        initialize_system()

        # 根据参数选择分析器配置
        analyzer = TradingSignalAnalyzer()
        if args.optimized:
            logger.info(f"使用优化模式，线程数: {args.workers}")
            analyzer.batch_processor = BatchProcessor(max_workers=args.workers)
        
        if args.scan:
            # 市场扫描模式
            date = args.date or time.strftime('%Y%m%d', time.localtime())
            # 验证日期格式
            try:
                datetime.strptime(date, '%Y%m%d')
            except ValueError:
                print(f"❌ 日期格式错误: {date}，请使用 YYYYMMDD 格式")
                return False
            print(f"🔍 开始市场扫描，目标日期: {date}")
            if args.optimized:
                print("⚡ 使用优化模式进行快速扫描...")
            results = analyzer.scan_market_signals(date, use_optimized=args.optimized)
            print_market_scan_results(results, analyzer.market_context, args.limit)
            extra_function()
        elif args.code:
            # 单股分析模式
            start_date = args.begin or ago_day_timestr(120, '%Y%m%d')
            end_date = args.end or args.date or time.strftime('%Y%m%d', time.localtime())
            # 单股分析时也可输出行情参考
            analyzer.analyze_market_context(end_date)
            analysis = analyzer.analyze_stock(args.code, start_date, end_date)
            if analysis:
                print_analysis_result(analysis, analyzer.market_context)
            else:
                print(f"❌ 分析股票 {args.code} 失败")
        else:
            print("请指定股票代码 (-c) 或使用市场扫描模式 (--scan)")
            parser.print_help()
        logger.info("✅ 股票买卖点分析工具执行完成")
        quant_monitor(27, True)
        return True
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        quant_monitor(27, False)
        return False
    except Exception as e:
        logger.error(f"股票买卖点分析工具执行失败: {e}")
        quant_monitor(27, False)
        return False

if __name__ == '__main__':
    setup_signal_handler()
    
    try:
        success = main()
        cleanup_and_exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        cleanup_and_exit(1)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        cleanup_and_exit(1) 